(()=>{var e={};e.id=520,e.ids=[520],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var o=t(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},614:(e,r,t)=>{Promise.resolve().then(t.bind(t,7243)),Promise.resolve().then(t.bind(t,9785))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var o=t(5986),n=t(8974);function a(...e){return(0,n.QP)((0,o.$)(e))}},1135:()=>{},1388:(e,r,t)=>{"use strict";t.d(r,{Tabs:()=>et,TabsContent:()=>ea,TabsList:()=>eo,TabsTrigger:()=>en});var o=t(687),n=t(3210),a=t.t(n,2);function s(e,r,{checkForDefaultPrevented:t=!0}={}){return function(o){if(e?.(o),!1===t||!o.defaultPrevented)return r?.(o)}}function i(e,r=[]){let t=[],a=()=>{let r=t.map(e=>n.createContext(e));return function(t){let o=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return a.scopeName=e,[function(r,a){let s=n.createContext(a),i=t.length;t=[...t,a];let l=r=>{let{scope:t,children:a,...l}=r,c=t?.[e]?.[i]||s,d=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:d,children:a})};return l.displayName=r+"Provider",[l,function(t,o){let l=o?.[e]?.[i]||s,c=n.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((r,{useScope:t,scopeName:o})=>{let n=t(e)[`__scope${o}`];return{...r,...n}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:o}),[o])}};return t.scopeName=r.scopeName,t}(a,...r)]}var l=t(8599),c=t(8730),d=new WeakMap;function u(e,r){if("at"in Array.prototype)return Array.prototype.at.call(e,r);let t=function(e,r){let t=e.length,o=m(r),n=o>=0?o:t+o;return n<0||n>=t?-1:n}(e,r);return -1===t?void 0:e[t]}function m(e){return e!=e||0===e?0:Math.trunc(e)}var p=globalThis?.document?n.useLayoutEffect:()=>{},f=a[" useId ".trim().toString()]||(()=>void 0),b=0;function g(e){let[r,t]=n.useState(f());return p(()=>{e||t(e=>e??String(b++))},[e]),e||(r?`radix-${r}`:"")}var h=t(4163),v=a[" useInsertionEffect ".trim().toString()]||p;function x({prop:e,defaultProp:r,onChange:t=()=>{},caller:o}){let[a,s,i]=function({defaultProp:e,onChange:r}){let[t,o]=n.useState(e),a=n.useRef(t),s=n.useRef(r);return v(()=>{s.current=r},[r]),n.useEffect(()=>{a.current!==t&&(s.current?.(t),a.current=t)},[t,a]),[t,o,s]}({defaultProp:r,onChange:t}),l=void 0!==e,c=l?e:a;{let r=n.useRef(void 0!==e);n.useEffect(()=>{let e=r.current;if(e!==l){let r=l?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${r}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}r.current=l},[l,o])}return[c,n.useCallback(r=>{if(l){let t="function"==typeof r?r(e):r;t!==e&&i.current?.(t)}else s(r)},[l,e,s,i])]}Symbol("RADIX:SYNC_STATE");var w=n.createContext(void 0);function y(e){let r=n.useContext(w);return e||r||"ltr"}var k="rovingFocusGroup.onEntryFocus",j={bubbles:!1,cancelable:!0},N="RovingFocusGroup",[C,P,T]=function(e){let r=e+"CollectionProvider",[t,a]=i(r),[s,d]=t(r,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:r,children:t}=e,a=n.useRef(null),i=n.useRef(new Map).current;return(0,o.jsx)(s,{scope:r,itemMap:i,collectionRef:a,children:t})};u.displayName=r;let m=e+"CollectionSlot",p=(0,c.TL)(m),f=n.forwardRef((e,r)=>{let{scope:t,children:n}=e,a=d(m,t),s=(0,l.s)(r,a.collectionRef);return(0,o.jsx)(p,{ref:s,children:n})});f.displayName=m;let b=e+"CollectionItemSlot",g="data-radix-collection-item",h=(0,c.TL)(b),v=n.forwardRef((e,r)=>{let{scope:t,children:a,...s}=e,i=n.useRef(null),c=(0,l.s)(r,i),u=d(b,t);return n.useEffect(()=>(u.itemMap.set(i,{ref:i,...s}),()=>void u.itemMap.delete(i))),(0,o.jsx)(h,{...{[g]:""},ref:c,children:a})});return v.displayName=b,[{Provider:u,Slot:f,ItemSlot:v},function(r){let t=d(e+"CollectionConsumer",r);return n.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(t.itemMap.values()).sort((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},a]}(N),[_,z]=i(N,[T]),[A,E]=_(N),R=n.forwardRef((e,r)=>(0,o.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(I,{...e,ref:r})})}));R.displayName=N;var I=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:i=!1,dir:c,currentTabStopId:d,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:f=!1,...b}=e,g=n.useRef(null),v=(0,l.s)(r,g),w=y(c),[C,T]=x({prop:d,defaultProp:u??null,onChange:m,caller:N}),[_,z]=n.useState(!1),E=function(e){let r=n.useRef(e);return n.useEffect(()=>{r.current=e}),n.useMemo(()=>(...e)=>r.current?.(...e),[])}(p),R=P(t),I=n.useRef(!1),[M,S]=n.useState(0);return n.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(k,E),()=>e.removeEventListener(k,E)},[E]),(0,o.jsx)(A,{scope:t,orientation:a,dir:w,loop:i,currentTabStopId:C,onItemFocus:n.useCallback(e=>T(e),[T]),onItemShiftTab:n.useCallback(()=>z(!0),[]),onFocusableItemAdd:n.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>S(e=>e-1),[]),children:(0,o.jsx)(h.sG.div,{tabIndex:_||0===M?-1:0,"data-orientation":a,...b,ref:v,style:{outline:"none",...e.style},onMouseDown:s(e.onMouseDown,()=>{I.current=!0}),onFocus:s(e.onFocus,e=>{let r=!I.current;if(e.target===e.currentTarget&&r&&!_){let r=new CustomEvent(k,j);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=R().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),f)}}I.current=!1}),onBlur:s(e.onBlur,()=>z(!1))})})}),M="RovingFocusGroupItem",S=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:i=!1,tabStopId:l,children:c,...d}=e,u=g(),m=l||u,p=E(M,t),f=p.currentTabStopId===m,b=P(t),{onFocusableItemAdd:v,onFocusableItemRemove:x,currentTabStopId:w}=p;return n.useEffect(()=>{if(a)return v(),()=>x()},[a,v,x]),(0,o.jsx)(C.ItemSlot,{scope:t,id:m,focusable:a,active:i,children:(0,o.jsx)(h.sG.span,{tabIndex:f?0:-1,"data-orientation":p.orientation,...d,ref:r,onMouseDown:s(e.onMouseDown,e=>{a?p.onItemFocus(m):e.preventDefault()}),onFocus:s(e.onFocus,()=>p.onItemFocus(m)),onKeyDown:s(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var o;let n=(o=e.key,"rtl"!==t?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(n)))return L[n]}(e,p.orientation,p.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let o=t.indexOf(e.currentTarget);t=p.loop?function(e,r){return e.map((t,o)=>e[(r+o)%e.length])}(t,o+1):t.slice(o+1)}setTimeout(()=>D(t))}}),children:"function"==typeof c?c({isCurrentTabStop:f,hasTabStop:null!=w}):c})})});S.displayName=M;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e,r=!1){let t=document.activeElement;for(let o of e)if(o===t||(o.focus({preventScroll:r}),document.activeElement!==t))return}var F=e=>{let{present:r,children:t}=e,o=function(e){var r,t;let[o,a]=n.useState(),s=n.useRef(null),i=n.useRef(e),l=n.useRef("none"),[c,d]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,r)=>t[e][r]??e,r));return n.useEffect(()=>{let e=$(s.current);l.current="mounted"===c?e:"none"},[c]),p(()=>{let r=s.current,t=i.current;if(t!==e){let o=l.current,n=$(r);e?d("MOUNT"):"none"===n||r?.display==="none"?d("UNMOUNT"):t&&o!==n?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),p(()=>{if(o){let e,r=o.ownerDocument.defaultView??window,t=t=>{let n=$(s.current).includes(t.animationName);if(t.target===o&&n&&(d("ANIMATION_END"),!i.current)){let t=o.style.animationFillMode;o.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=t)})}},n=e=>{e.target===o&&(l.current=$(s.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(r),a="function"==typeof t?t({present:o.isPresent}):n.Children.only(t),s=(0,l.s)(o.ref,function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof t||o.isPresent?n.cloneElement(a,{ref:s}):null};function $(e){return e?.animationName||"none"}F.displayName="Presence";var O="Tabs",[U,G]=i(O,[z]),q=z(),[W,B]=U(O),K=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,onValueChange:a,defaultValue:s,orientation:i="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=y(l),[m,p]=x({prop:n,onChange:a,defaultProp:s??"",caller:O});return(0,o.jsx)(W,{scope:t,baseId:g(),value:m,onValueChange:p,orientation:i,dir:u,activationMode:c,children:(0,o.jsx)(h.sG.div,{dir:u,"data-orientation":i,...d,ref:r})})});K.displayName=O;var V="TabsList",Z=n.forwardRef((e,r)=>{let{__scopeTabs:t,loop:n=!0,...a}=e,s=B(V,t),i=q(t);return(0,o.jsx)(R,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:n,children:(0,o.jsx)(h.sG.div,{role:"tablist","aria-orientation":s.orientation,...a,ref:r})})});Z.displayName=V;var X="TabsTrigger",Y=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,disabled:a=!1,...i}=e,l=B(X,t),c=q(t),d=J(l.baseId,n),u=ee(l.baseId,n),m=n===l.value;return(0,o.jsx)(S,{asChild:!0,...c,focusable:!a,active:m,children:(0,o.jsx)(h.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...i,ref:r,onMouseDown:s(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:s(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:s(e.onFocus,()=>{let e="manual"!==l.activationMode;m||a||!e||l.onValueChange(n)})})})});Y.displayName=X;var H="TabsContent",Q=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:a,forceMount:s,children:i,...l}=e,c=B(H,t),d=J(c.baseId,a),u=ee(c.baseId,a),m=a===c.value,p=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(F,{present:s||m,children:({present:t})=>(0,o.jsx)(h.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!t,id:u,tabIndex:0,...l,ref:r,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&i})})});function J(e,r){return`${e}-trigger-${r}`}function ee(e,r){return`${e}-content-${r}`}Q.displayName=H;var er=t(6360);function et({className:e,...r}){return(0,o.jsx)(K,{"data-slot":"tabs",className:(0,er.cn)("flex flex-col gap-2",e),...r})}function eo({className:e,...r}){return(0,o.jsx)(Z,{"data-slot":"tabs-list",className:(0,er.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...r})}function en({className:e,...r}){return(0,o.jsx)(Y,{"data-slot":"tabs-trigger",className:(0,er.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function ea({className:e,...r}){return(0,o.jsx)(Q,{"data-slot":"tabs-content",className:(0,er.cn)("flex-1 outline-none",e),...r})}},1427:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>i,TN:()=>l});var o=t(7413);t(1120);var n=t(662),a=t(974);let s=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...t}){return(0,o.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,a.cn)(s({variant:r}),e),...t})}function l({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"alert-description",className:(0,a.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=1906,e.exports=r},1997:e=>{"use strict";e.exports=require("punycode")},2295:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},2359:(e,r,t)=>{"use strict";t.d(r,{iD:()=>d,S6:()=>m,$5:()=>u});var o=t(7218);t(9130);var n=t(2351),a=t(9916),s=t(2507);async function i(){return await (0,s.U)()}async function l(e){let r=await i(),{data:t,error:o}=await r.from("users").insert(e).select().single();if(o)throw o;return t}async function c(e){let r=await i(),{data:t,error:o}=await r.from("users").select("*").eq("id",e).single();if(o)throw o;return t}async function d(e){let r=await (0,s.U)(),t=e.get("email"),o=e.get("password");t&&o||(0,a.redirect)("/login?error=missing-credentials");let{data:i,error:d}=await r.auth.signInWithPassword({email:t,password:o});if(d&&(0,a.redirect)(`/login?error=${encodeURIComponent(d.message)}`),i.user)try{await c(i.user.id)}catch{await l({id:i.user.id,email:i.user.email,instagram_handle:null,business_name:null,mastra_agent_config:{}})}(0,n.revalidatePath)("/","layout"),(0,a.redirect)("/dashboard")}async function u(e){let r=await (0,s.U)(),t=e.get("email"),o=e.get("password"),i=e.get("business_name");t&&o||(0,a.redirect)("/login?error=missing-credentials");let{data:c,error:d}=await r.auth.signUp({email:t,password:o,options:{emailRedirectTo:`${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`}});if(d&&(0,a.redirect)(`/login?error=${encodeURIComponent(d.message)}`),c.user)try{await l({id:c.user.id,email:c.user.email,instagram_handle:null,business_name:i||null,mastra_agent_config:{}})}catch(e){console.error("Error creating user profile:",e)}(0,n.revalidatePath)("/","layout"),(0,a.redirect)("/login?message=check-email")}async function m(){let e=await (0,s.U)(),{error:r}=await e.auth.signOut();r&&(0,a.redirect)("/error"),(0,n.revalidatePath)("/","layout"),(0,a.redirect)("/login")}(0,t(7478).D)([d,u,m]),(0,o.A)(d,"404ffb6408f3758ef54519a18c70686620246b4b96",null),(0,o.A)(u,"403b9a20870bf87801d4fe1364ea25b88da2e64fba",null),(0,o.A)(m,"00f2852461826a1a20213f241ac21fd695c07bc6e1",null)},2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var o=t(261),n=t(4999);async function a(){let e=await (0,n.UL)();return(0,o.createServerClient)(process.env.NEXT_PUBLIC_SUPABASE_URL,process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:o})=>e.set(r,t,o))}catch{}}}})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3178:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3346:()=>{},3469:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var o=t(7413);t(1120);var n=t(403),a=t(662),s=t(974);let i=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:a=!1,...l}){let c=a?n.DX:"button";return(0,o.jsx)(c,{"data-slot":"button",className:(0,s.cn)(i({variant:r,size:t,className:e})),...l})}},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4163:(e,r,t)=>{"use strict";t.d(r,{sG:()=>s});var o=t(3210);t(1215);var n=t(8730),a=t(687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),s=o.forwardRef((e,o)=>{let{asChild:n,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?t:r,{...s,ref:o})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{})},4300:(e,r,t)=>{"use strict";t.d(r,{Label:()=>l});var o=t(687),n=t(3210),a=t(4163),s=n.forwardRef((e,r)=>(0,o.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));s.displayName="Label";var i=t(6360);function l({className:e,...r}){return(0,o.jsx)(s,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var o=t(7413),n=t(2376),a=t.n(n),s=t(8726),i=t.n(s);t(1135);let l={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${a().variable} ${i().variable} antialiased`,children:e})})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4924:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00f2852461826a1a20213f241ac21fd695c07bc6e1":()=>o.S6,"403b9a20870bf87801d4fe1364ea25b88da2e64fba":()=>o.$5,"404ffb6408f3758ef54519a18c70686620246b4b96":()=>o.iD});var o=t(2359)},5125:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var o=t(5239),n=t(8088),a=t(8170),s=t.n(a),i=t(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5803)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5343:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5803:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var o=t(7413),n=t(1120),a=t(2359),s=t(3469),i=t(974);function l({className:e,type:r,...t}){return(0,o.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}var c=t(7243),d=t(8963),u=t(9785),m=t(1427),p=t(7467);let f=(0,t(6373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);function b({searchParams:e}){return e.error?(0,o.jsxs)(m.Fc,{variant:"destructive",className:"mb-4",children:[(0,o.jsx)(p.A,{className:"h-4 w-4"}),(0,o.jsx)(m.TN,{children:"missing-credentials"===e.error?"Please enter both email and password":decodeURIComponent(e.error)})]}):"check-email"===e.message?(0,o.jsxs)(m.Fc,{className:"mb-4",children:[(0,o.jsx)(f,{className:"h-4 w-4"}),(0,o.jsx)(m.TN,{children:"Check your email for a confirmation link to complete your registration."})]}):null}async function g({searchParams:e}){let r=await e;return(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Welcome to Mawuena"}),(0,o.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Streamline your Instagram clothing business"})]}),(0,o.jsx)(n.Suspense,{fallback:(0,o.jsx)("div",{children:"Loading..."}),children:(0,o.jsx)(b,{searchParams:r})}),(0,o.jsxs)(u.Tabs,{defaultValue:"login",className:"w-full",children:[(0,o.jsxs)(u.TabsList,{className:"grid w-full grid-cols-2",children:[(0,o.jsx)(u.TabsTrigger,{value:"login",children:"Sign In"}),(0,o.jsx)(u.TabsTrigger,{value:"signup",children:"Sign Up"})]}),(0,o.jsx)(u.TabsContent,{value:"login",children:(0,o.jsxs)(d.Zp,{children:[(0,o.jsxs)(d.aR,{children:[(0,o.jsx)(d.ZB,{children:"Sign In"}),(0,o.jsx)(d.BT,{children:"Enter your credentials to access your account"})]}),(0,o.jsx)(d.Wu,{children:(0,o.jsxs)("form",{action:a.iD,className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(c.Label,{htmlFor:"email",children:"Email"}),(0,o.jsx)(l,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,placeholder:"<EMAIL>"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(c.Label,{htmlFor:"password",children:"Password"}),(0,o.jsx)(l,{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,placeholder:"••••••••"})]}),(0,o.jsx)(s.$,{type:"submit",className:"w-full",children:"Sign In"})]})})]})}),(0,o.jsx)(u.TabsContent,{value:"signup",children:(0,o.jsxs)(d.Zp,{children:[(0,o.jsxs)(d.aR,{children:[(0,o.jsx)(d.ZB,{children:"Create Account"}),(0,o.jsx)(d.BT,{children:"Start managing your clothing business on Instagram"})]}),(0,o.jsx)(d.Wu,{children:(0,o.jsxs)("form",{action:a.$5,className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(c.Label,{htmlFor:"business_name",children:"Business Name (Optional)"}),(0,o.jsx)(l,{id:"business_name",name:"business_name",type:"text",placeholder:"Your Fashion Store"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(c.Label,{htmlFor:"signup-email",children:"Email"}),(0,o.jsx)(l,{id:"signup-email",name:"email",type:"email",autoComplete:"email",required:!0,placeholder:"<EMAIL>"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(c.Label,{htmlFor:"signup-password",children:"Password"}),(0,o.jsx)(l,{id:"signup-password",name:"password",type:"password",autoComplete:"new-password",required:!0,placeholder:"••••••••",minLength:6})]}),(0,o.jsx)(s.$,{type:"submit",className:"w-full",children:"Create Account"}),(0,o.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"By creating an account, you agree to our Terms of Service and Privacy Policy"})]})})]})})]})]})})}},5886:(e,r,t)=>{Promise.resolve().then(t.bind(t,4300)),Promise.resolve().then(t.bind(t,1388))},6360:(e,r,t)=>{"use strict";t.d(r,{cn:()=>ed});let o=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),n(t,r)||s(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},a=/^\[(.+)\]$/,s=e=>{if(a.test(e)){let r=a.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},i=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)l(t[e],o,e,r);return o},l=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:c(r,e)).classGroupId=t;return}if("function"==typeof e)return d(e)?void l(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{l(n,c(r,e),t,o)})})},c=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},m=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,n=0,a=0;for(let s=0;s<e.length;s++){let i=e[s];if(0===o&&0===n){if(":"===i){t.push(e.slice(a,s)),a=s+1;continue}if("/"===i){r=s;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let s=0===t.length?e:e.substring(a),i=p(s);return{modifiers:t,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},b=e=>({cache:u(e.cacheSize),parseClassName:m(e),sortModifiers:f(e),...o(e)}),g=/\s+/,h=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,s=[],i=e.trim().split(g),l="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=t(r);if(c){l=r+(l.length>0?" "+l:l);continue}let f=!!p,b=o(f?m.substring(0,p):m);if(!b){if(!f||!(b=o(m))){l=r+(l.length>0?" "+l:l);continue}f=!1}let g=a(d).join(":"),h=u?g+"!":g,v=h+b;if(s.includes(v))continue;s.push(v);let x=n(b,f);for(let e=0;e<x.length;++e){let r=x[e];s.push(h+r)}l=r+(l.length>0?" "+l:l)}return l};function v(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=x(e))&&(o&&(o+=" "),o+=r);return o}let x=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=x(e[o]))&&(t&&(t+=" "),t+=r);return t},w=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},y=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,j=/^\d+\/\d+$/,N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,T=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,z=e=>j.test(e),A=e=>!!e&&!Number.isNaN(Number(e)),E=e=>!!e&&Number.isInteger(Number(e)),R=e=>e.endsWith("%")&&A(e.slice(0,-1)),I=e=>N.test(e),M=()=>!0,S=e=>C.test(e)&&!P.test(e),L=()=>!1,D=e=>T.test(e),F=e=>_.test(e),$=e=>!U(e)&&!V(e),O=e=>ee(e,en,L),U=e=>y.test(e),G=e=>ee(e,ea,S),q=e=>ee(e,es,A),W=e=>ee(e,et,L),B=e=>ee(e,eo,F),K=e=>ee(e,el,D),V=e=>k.test(e),Z=e=>er(e,ea),X=e=>er(e,ei),Y=e=>er(e,et),H=e=>er(e,en),Q=e=>er(e,eo),J=e=>er(e,el,!0),ee=(e,r,t)=>{let o=y.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=k.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,es=e=>"number"===e,ei=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...r){let t,o,n,a=function(i){return o=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,a=s,s(i)};function s(e){let r=o(e);if(r)return r;let a=h(e,t);return n(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=w("color"),r=w("font"),t=w("text"),o=w("font-weight"),n=w("tracking"),a=w("leading"),s=w("breakpoint"),i=w("container"),l=w("spacing"),c=w("radius"),d=w("shadow"),u=w("inset-shadow"),m=w("text-shadow"),p=w("drop-shadow"),f=w("blur"),b=w("perspective"),g=w("aspect"),h=w("ease"),v=w("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...y(),V,U],j=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[V,U,l],P=()=>[z,"full","auto",...C()],T=()=>[E,"none","subgrid",V,U],_=()=>["auto",{span:["full",E,V,U]},E,V,U],S=()=>[E,"auto",V,U],L=()=>["auto","min","max","fr",V,U],D=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],er=()=>[z,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],et=()=>[e,V,U],eo=()=>[...y(),Y,W,{position:[V,U]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",H,O,{size:[V,U]}],es=()=>[R,Z,G],ei=()=>["","none","full",c,V,U],el=()=>["",A,Z,G],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[A,R,Y,W],em=()=>["","none",f,V,U],ep=()=>["none",A,V,U],ef=()=>["none",A,V,U],eb=()=>[A,V,U],eg=()=>[z,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[I],breakpoint:[I],color:[M],container:[I],"drop-shadow":[I],ease:["in","out","in-out"],font:[$],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[I],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[I],shadow:[I],spacing:["px",A],text:[I],"text-shadow":[I],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",z,U,V,g]}],container:["container"],columns:[{columns:[A,U,V,i]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[E,"auto",V,U]}],basis:[{basis:[z,"full","auto",i,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[A,z,"auto","initial","none",U]}],grow:[{grow:["",A,V,U]}],shrink:[{shrink:["",A,V,U]}],order:[{order:[E,"first","last","none",V,U]}],"grid-cols":[{"grid-cols":T()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":T()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":L()}],"auto-rows":[{"auto-rows":L()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...D(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...D()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":D()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[i,"screen",...er()]}],"min-w":[{"min-w":[i,"screen","none",...er()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[s]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,Z,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,V,q]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,U]}],"font-family":[{font:[X,U,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,V,U]}],"line-clamp":[{"line-clamp":[A,"none",V,q]}],leading:[{leading:[a,...C()]}],"list-image":[{"list-image":["none",V,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",V,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[A,"from-font","auto",V,G]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[A,"auto",V,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},E,V,U],radial:["",V,U],conic:[E,V,U]},Q,B]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[A,V,U]}],"outline-w":[{outline:["",A,Z,G]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",d,J,K]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",u,J,K]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[A,G]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",m,J,K]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[A,V,U]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[A]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[V,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[A]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",V,U]}],filter:[{filter:["","none",V,U]}],blur:[{blur:em()}],brightness:[{brightness:[A,V,U]}],contrast:[{contrast:[A,V,U]}],"drop-shadow":[{"drop-shadow":["","none",p,J,K]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",A,V,U]}],"hue-rotate":[{"hue-rotate":[A,V,U]}],invert:[{invert:["",A,V,U]}],saturate:[{saturate:[A,V,U]}],sepia:[{sepia:["",A,V,U]}],"backdrop-filter":[{"backdrop-filter":["","none",V,U]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[A,V,U]}],"backdrop-contrast":[{"backdrop-contrast":[A,V,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",A,V,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[A,V,U]}],"backdrop-invert":[{"backdrop-invert":["",A,V,U]}],"backdrop-opacity":[{"backdrop-opacity":[A,V,U]}],"backdrop-saturate":[{"backdrop-saturate":[A,V,U]}],"backdrop-sepia":[{"backdrop-sepia":["",A,V,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",V,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[A,"initial",V,U]}],ease:[{ease:["linear","initial",h,V,U]}],delay:[{delay:[A,V,U]}],animate:[{animate:["none",v,V,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,V,U]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[V,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V,U]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[A,Z,G,q]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ed(...e){return ec(function(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}(e))}},6373:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var o=t(1120);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),s=e=>{let r=a(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:a="",children:s,iconNode:d,...u},m)=>(0,o.createElement)("svg",{ref:m,...c,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:i("lucide",a),...!s&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,r])=>(0,o.createElement)(e,r)),...Array.isArray(s)?s:[s]])),u=(e,r)=>{let t=(0,o.forwardRef)(({className:t,...a},l)=>(0,o.createElement)(d,{ref:l,iconNode:r,className:i(`lucide-${n(s(e))}`,`lucide-${e}`,t),...a}));return t.displayName=s(e),t}},7243:(e,r,t)=>{"use strict";t.d(r,{Label:()=>o});let o=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx","Label")},7467:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});let o=(0,t(6373).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8599:(e,r,t)=>{"use strict";t.d(r,{s:()=>s,t:()=>a});var o=t(3210);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function s(...e){return o.useCallback(a(...e),e)}},8730:(e,r,t)=>{"use strict";t.d(r,{TL:()=>s});var o=t(3210),n=t(8599),a=t(687);function s(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...a}=e;if(o.isValidElement(t)){var s;let e,i,l=(s=t,(i=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(i=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),c=function(e,r){let t={...r};for(let o in r){let n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...a}:"className"===o&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==o.Fragment&&(c.ref=r?(0,n.t)(r,l):l),o.cloneElement(t,c)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...s}=e,i=o.Children.toArray(n),c=i.find(l);if(c){let e=c.props.children,n=i.map(r=>r!==c?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...s,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...s,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var i=Symbol("radix.slottable");function l(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},8963:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>a,aR:()=>s});var o=t(7413);t(1120);var n=t(974);function a({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function s({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function i({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function c({className:e,...r}){return(0,o.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{},9785:(e,r,t)=>{"use strict";t.d(r,{Tabs:()=>n,TabsContent:()=>i,TabsList:()=>a,TabsTrigger:()=>s});var o=t(2907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx","Tabs"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx","TabsList"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx","TabsTrigger"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx","TabsContent")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,145,875,658,923,877],()=>t(5125));module.exports=o})();