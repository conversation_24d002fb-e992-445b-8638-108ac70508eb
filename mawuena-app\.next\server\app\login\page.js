(()=>{var m={};m.id=520,m.ids=[520],m.modules={440:(m,b,x)=>{"use strict";x.r(b),x.d(b,{default:()=>n});var e=x(1658);let n=async m=>[{type:"image/x-icon",sizes:"16x16",url:(0,e.fillMetadataSegment)(".",await m.params,"favicon.ico")+""}]},846:m=>{"use strict";m.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2295:(m,b,x)=>{Promise.resolve().then(x.t.bind(x,6346,23)),Promise.resolve().then(x.t.bind(x,7924,23)),Promise.resolve().then(x.t.bind(x,5656,23)),Promise.resolve().then(x.t.bind(x,99,23)),Promise.resolve().then(x.t.bind(x,8243,23)),Promise.resolve().then(x.t.bind(x,8827,23)),Promise.resolve().then(x.t.bind(x,2763,23)),Promise.resolve().then(x.t.bind(x,7173,23))},3033:m=>{"use strict";m.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3178:()=>{},3295:m=>{"use strict";m.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3346:()=>{},3873:m=>{"use strict";m.exports=require("path")},4431:(m,b,x)=>{"use strict";x.r(b),x.d(b,{default:()=>i,metadata:()=>s});var e=x(7413),n=x(2376),r=x.n(n),a=x(8726),t=x.n(a);x(1135);let s={title:"Create Next App",description:"Generated by create next app"};function i({children:m}){return(0,e.jsx)("html",{lang:"en",children:(0,e.jsx)("body",{className:`${r().variable} ${t().variable} antialiased`,children:m})})}},4934:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Return statement is not allowed here\n     ,-[\x1b[36;1;4mD:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx\x1b[0m:10:1]\n \x1b[2m  7\x1b[0m |     import { Tabs, TabsContent, TabsList, TabsTrigger } from \'@/components/ui/tabs\'\n \x1b[2m  8\x1b[0m |     import { Alert, AlertDescription } from \'@/components/ui/alert\'\n \x1b[2m  9\x1b[0m |     import { AlertCircle, CheckCircle } from \'lucide-react\'\n \x1b[2m 10\x1b[0m | \x1b[35;1m,\x1b[0m\x1b[35;1m-\x1b[0m\x1b[35;1m>\x1b[0m   return (\n \x1b[2m 11\x1b[0m | \x1b[35;1m|\x1b[0m       <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">\n \x1b[2m 12\x1b[0m | \x1b[35;1m|\x1b[0m         <div className="max-w-md w-full space-y-8">\n \x1b[2m 13\x1b[0m | \x1b[35;1m|\x1b[0m           <div className="text-center">\n \x1b[2m 14\x1b[0m | \x1b[35;1m|\x1b[0m             <h2 className="mt-6 text-3xl font-bold text-gray-900">\n \x1b[2m 15\x1b[0m | \x1b[35;1m|\x1b[0m               Welcome to Mawuena\n \x1b[2m 16\x1b[0m | \x1b[35;1m|\x1b[0m             </h2>\n \x1b[2m 17\x1b[0m | \x1b[35;1m|\x1b[0m             <p className="mt-2 text-sm text-gray-600">\n \x1b[2m 18\x1b[0m | \x1b[35;1m|\x1b[0m               Streamline your Instagram clothing business\n \x1b[2m 19\x1b[0m | \x1b[35;1m|\x1b[0m             </p>\n \x1b[2m 20\x1b[0m | \x1b[35;1m|\x1b[0m           </div>\n \x1b[2m 21\x1b[0m | \x1b[35;1m|\x1b[0m   \n \x1b[2m 22\x1b[0m | \x1b[35;1m|\x1b[0m           <Tabs defaultValue="login" className="w-full">\n \x1b[2m 23\x1b[0m | \x1b[35;1m|\x1b[0m             <TabsList className="grid w-full grid-cols-2">\n \x1b[2m 24\x1b[0m | \x1b[35;1m|\x1b[0m               <TabsTrigger value="login">Sign In</TabsTrigger>\n \x1b[2m 25\x1b[0m | \x1b[35;1m|\x1b[0m               <TabsTrigger value="signup">Sign Up</TabsTrigger>\n \x1b[2m 26\x1b[0m | \x1b[35;1m|\x1b[0m             </TabsList>\n \x1b[2m 27\x1b[0m | \x1b[35;1m|\x1b[0m   \n \x1b[2m 28\x1b[0m | \x1b[35;1m|\x1b[0m             <TabsContent value="login">\n \x1b[2m 29\x1b[0m | \x1b[35;1m|\x1b[0m               <Card>\n \x1b[2m 30\x1b[0m | \x1b[35;1m|\x1b[0m                 <CardHeader>\n \x1b[2m 31\x1b[0m | \x1b[35;1m|\x1b[0m                   <CardTitle>Sign In</CardTitle>\n \x1b[2m 32\x1b[0m | \x1b[35;1m|\x1b[0m                   <CardDescription>\n \x1b[2m 33\x1b[0m | \x1b[35;1m|\x1b[0m                     Enter your credentials to access your account\n \x1b[2m 34\x1b[0m | \x1b[35;1m|\x1b[0m                   </CardDescription>\n \x1b[2m 35\x1b[0m | \x1b[35;1m|\x1b[0m                 </CardHeader>\n \x1b[2m 36\x1b[0m | \x1b[35;1m|\x1b[0m                 <CardContent>\n \x1b[2m 37\x1b[0m | \x1b[35;1m|\x1b[0m                   <form action={login} className="space-y-4">\n \x1b[2m 38\x1b[0m | \x1b[35;1m|\x1b[0m                     <div className="space-y-2">\n \x1b[2m 39\x1b[0m | \x1b[35;1m|\x1b[0m                       <Label htmlFor="email">Email</Label>\n \x1b[2m 40\x1b[0m | \x1b[35;1m|\x1b[0m                       <Input\n \x1b[2m 41\x1b[0m | \x1b[35;1m|\x1b[0m                         id="email"\n \x1b[2m 42\x1b[0m | \x1b[35;1m|\x1b[0m                         name="email"\n \x1b[2m 43\x1b[0m | \x1b[35;1m|\x1b[0m                         type="email"\n \x1b[2m 44\x1b[0m | \x1b[35;1m|\x1b[0m                         autoComplete="email"\n \x1b[2m 45\x1b[0m | \x1b[35;1m|\x1b[0m                         required\n \x1b[2m 46\x1b[0m | \x1b[35;1m|\x1b[0m                         placeholder="<EMAIL>"\n \x1b[2m 47\x1b[0m | \x1b[35;1m|\x1b[0m                       />\n \x1b[2m 48\x1b[0m | \x1b[35;1m|\x1b[0m                     </div>\n \x1b[2m 49\x1b[0m | \x1b[35;1m|\x1b[0m                     <div className="space-y-2">\n \x1b[2m 50\x1b[0m | \x1b[35;1m|\x1b[0m                       <Label htmlFor="password">Password</Label>\n \x1b[2m 51\x1b[0m | \x1b[35;1m|\x1b[0m                       <Input\n \x1b[2m 52\x1b[0m | \x1b[35;1m|\x1b[0m                         id="password"\n \x1b[2m 53\x1b[0m | \x1b[35;1m|\x1b[0m                         name="password"\n \x1b[2m 54\x1b[0m | \x1b[35;1m|\x1b[0m                         type="password"\n \x1b[2m 55\x1b[0m | \x1b[35;1m|\x1b[0m                         autoComplete="current-password"\n \x1b[2m 56\x1b[0m | \x1b[35;1m|\x1b[0m                         required\n \x1b[2m 57\x1b[0m | \x1b[35;1m|\x1b[0m                         placeholder="••••••••"\n \x1b[2m 58\x1b[0m | \x1b[35;1m|\x1b[0m                       />\n \x1b[2m 59\x1b[0m | \x1b[35;1m|\x1b[0m                     </div>\n \x1b[2m 60\x1b[0m | \x1b[35;1m|\x1b[0m                     <Button type="submit" className="w-full">\n \x1b[2m 61\x1b[0m | \x1b[35;1m|\x1b[0m                       Sign In\n \x1b[2m 62\x1b[0m | \x1b[35;1m|\x1b[0m                     </Button>\n \x1b[2m 63\x1b[0m | \x1b[35;1m|\x1b[0m                   </form>\n \x1b[2m 64\x1b[0m | \x1b[35;1m|\x1b[0m                 </CardContent>\n \x1b[2m 65\x1b[0m | \x1b[35;1m|\x1b[0m               </Card>\n \x1b[2m 66\x1b[0m | \x1b[35;1m|\x1b[0m             </TabsContent>\n \x1b[2m 67\x1b[0m | \x1b[35;1m|\x1b[0m   \n \x1b[2m 68\x1b[0m | \x1b[35;1m|\x1b[0m             <TabsContent value="signup">\n \x1b[2m 69\x1b[0m | \x1b[35;1m|\x1b[0m               <Card>\n \x1b[2m 70\x1b[0m | \x1b[35;1m|\x1b[0m                 <CardHeader>\n \x1b[2m 71\x1b[0m | \x1b[35;1m|\x1b[0m                   <CardTitle>Create Account</CardTitle>\n \x1b[2m 72\x1b[0m | \x1b[35;1m|\x1b[0m                   <CardDescription>\n \x1b[2m 73\x1b[0m | \x1b[35;1m|\x1b[0m                     Start managing your clothing business on Instagram\n \x1b[2m 74\x1b[0m | \x1b[35;1m|\x1b[0m                   </CardDescription>\n \x1b[2m 75\x1b[0m | \x1b[35;1m|\x1b[0m                 </CardHeader>\n \x1b[2m 76\x1b[0m | \x1b[35;1m|\x1b[0m                 <CardContent>\n \x1b[2m 77\x1b[0m | \x1b[35;1m|\x1b[0m                   <form action={signup} className="space-y-4">\n \x1b[2m 78\x1b[0m | \x1b[35;1m|\x1b[0m                     <div className="space-y-2">\n \x1b[2m 79\x1b[0m | \x1b[35;1m|\x1b[0m                       <Label htmlFor="business_name">Business Name (Optional)</Label>\n \x1b[2m 80\x1b[0m | \x1b[35;1m|\x1b[0m                       <Input\n \x1b[2m 81\x1b[0m | \x1b[35;1m|\x1b[0m                         id="business_name"\n \x1b[2m 82\x1b[0m | \x1b[35;1m|\x1b[0m                         name="business_name"\n \x1b[2m 83\x1b[0m | \x1b[35;1m|\x1b[0m                         type="text"\n \x1b[2m 84\x1b[0m | \x1b[35;1m|\x1b[0m                         placeholder="Your Fashion Store"\n \x1b[2m 85\x1b[0m | \x1b[35;1m|\x1b[0m                       />\n \x1b[2m 86\x1b[0m | \x1b[35;1m|\x1b[0m                     </div>\n \x1b[2m 87\x1b[0m | \x1b[35;1m|\x1b[0m                     <div className="space-y-2">\n \x1b[2m 88\x1b[0m | \x1b[35;1m|\x1b[0m                       <Label htmlFor="signup-email">Email</Label>\n \x1b[2m 89\x1b[0m | \x1b[35;1m|\x1b[0m                       <Input\n \x1b[2m 90\x1b[0m | \x1b[35;1m|\x1b[0m                         id="signup-email"\n \x1b[2m 91\x1b[0m | \x1b[35;1m|\x1b[0m                         name="email"\n \x1b[2m 92\x1b[0m | \x1b[35;1m|\x1b[0m                         type="email"\n \x1b[2m 93\x1b[0m | \x1b[35;1m|\x1b[0m                         autoComplete="email"\n \x1b[2m 94\x1b[0m | \x1b[35;1m|\x1b[0m                         required\n \x1b[2m 95\x1b[0m | \x1b[35;1m|\x1b[0m                         placeholder="<EMAIL>"\n \x1b[2m 96\x1b[0m | \x1b[35;1m|\x1b[0m                       />\n \x1b[2m 97\x1b[0m | \x1b[35;1m|\x1b[0m                     </div>\n \x1b[2m 98\x1b[0m | \x1b[35;1m|\x1b[0m                     <div className="space-y-2">\n \x1b[2m 99\x1b[0m | \x1b[35;1m|\x1b[0m                       <Label htmlFor="signup-password">Password</Label>\n \x1b[2m100\x1b[0m | \x1b[35;1m|\x1b[0m                       <Input\n \x1b[2m101\x1b[0m | \x1b[35;1m|\x1b[0m                         id="signup-password"\n \x1b[2m102\x1b[0m | \x1b[35;1m|\x1b[0m                         name="password"\n \x1b[2m103\x1b[0m | \x1b[35;1m|\x1b[0m                         type="password"\n \x1b[2m104\x1b[0m | \x1b[35;1m|\x1b[0m                         autoComplete="new-password"\n \x1b[2m105\x1b[0m | \x1b[35;1m|\x1b[0m                         required\n \x1b[2m106\x1b[0m | \x1b[35;1m|\x1b[0m                         placeholder="••••••••"\n \x1b[2m107\x1b[0m | \x1b[35;1m|\x1b[0m                         minLength={6}\n \x1b[2m108\x1b[0m | \x1b[35;1m|\x1b[0m                       />\n \x1b[2m109\x1b[0m | \x1b[35;1m|\x1b[0m                     </div>\n \x1b[2m110\x1b[0m | \x1b[35;1m|\x1b[0m                     <Button type="submit" className="w-full">\n \x1b[2m111\x1b[0m | \x1b[35;1m|\x1b[0m                       Create Account\n \x1b[2m112\x1b[0m | \x1b[35;1m|\x1b[0m                     </Button>\n \x1b[2m113\x1b[0m | \x1b[35;1m|\x1b[0m                     <p className="text-xs text-gray-500 text-center">\n \x1b[2m114\x1b[0m | \x1b[35;1m|\x1b[0m                       By creating an account, you agree to our Terms of Service and Privacy Policy\n \x1b[2m115\x1b[0m | \x1b[35;1m|\x1b[0m                     </p>\n \x1b[2m116\x1b[0m | \x1b[35;1m|\x1b[0m                   </form>\n \x1b[2m117\x1b[0m | \x1b[35;1m|\x1b[0m                 </CardContent>\n \x1b[2m118\x1b[0m | \x1b[35;1m|\x1b[0m               </Card>\n \x1b[2m119\x1b[0m | \x1b[35;1m|\x1b[0m             </TabsContent>\n \x1b[2m120\x1b[0m | \x1b[35;1m|\x1b[0m           </Tabs>\n \x1b[2m121\x1b[0m | \x1b[35;1m|\x1b[0m         </div>\n \x1b[2m122\x1b[0m | \x1b[35;1m|\x1b[0m       </div>\n \x1b[2m123\x1b[0m | \x1b[35;1m`\x1b[0m\x1b[35;1m-\x1b[0m\x1b[35;1m>\x1b[0m   )\n \x1b[2m124\x1b[0m |     }\n \x1b[2m125\x1b[0m |     \n \x1b[2m126\x1b[0m |     function AlertMessages({ searchParams }: { searchParams: { error?: string; message?: string } }) {\n     `----\n  \x1b[31mx\x1b[0m Expression expected\n     ,-[\x1b[36;1;4mD:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx\x1b[0m:124:1]\n \x1b[2m121\x1b[0m |       </div>\n \x1b[2m122\x1b[0m |     </div>\n \x1b[2m123\x1b[0m |   )\n \x1b[2m124\x1b[0m | }\n     : \x1b[35;1m^\x1b[0m\n \x1b[2m125\x1b[0m | \n \x1b[2m126\x1b[0m | function AlertMessages({ searchParams }: { searchParams: { error?: string; message?: string } }) {\n \x1b[2m127\x1b[0m |   if (searchParams.error) {\n     `----\n\n\nCaused by:\n    Syntax Error')},5125:(m,b,x)=>{"use strict";x.r(b),x.d(b,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>o,routeModule:()=>d,tree:()=>i});var e=x(5239),n=x(8088),r=x(8170),a=x.n(r),t=x(893),s={};for(let m in t)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(m)&&(s[m]=()=>t[m]);x.d(b,s);let i={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(x.bind(x,4934)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async m=>(await Promise.resolve().then(x.bind(x,440))).default(m)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(x.bind(x,4431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(x.t.bind(x,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(x.t.bind(x,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(x.t.bind(x,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async m=>(await Promise.resolve().then(x.bind(x,440))).default(m)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx"],l={require:x,loadChunk:()=>Promise.resolve()},d=new e.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},5343:(m,b,x)=>{Promise.resolve().then(x.t.bind(x,6444,23)),Promise.resolve().then(x.t.bind(x,6042,23)),Promise.resolve().then(x.t.bind(x,8170,23)),Promise.resolve().then(x.t.bind(x,9477,23)),Promise.resolve().then(x.t.bind(x,9345,23)),Promise.resolve().then(x.t.bind(x,2089,23)),Promise.resolve().then(x.t.bind(x,6577,23)),Promise.resolve().then(x.t.bind(x,1307,23))},6487:()=>{},8335:()=>{},9121:m=>{"use strict";m.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:m=>{"use strict";m.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:m=>{"use strict";m.exports=require("url")}};var b=require("../../webpack-runtime.js");b.C(m);var x=m=>b(b.s=m),e=b.X(0,[447,145,658],()=>x(5125));module.exports=e})();