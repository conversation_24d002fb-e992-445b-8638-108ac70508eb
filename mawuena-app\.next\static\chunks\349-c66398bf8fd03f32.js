"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[349],{968:(e,r,o)=>{o.d(r,{b:()=>s});var t=o(2115),n=o(3655),l=o(5155),a=t.forwardRef((e,r)=>(0,l.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{var o;r.target.closest("button, input, select, textarea")||(null==(o=e.onMouseDown)||o.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var s=a},1285:(e,r,o)=>{o.d(r,{B:()=>i});var t,n=o(2115),l=o(2712),a=(t||(t=o.t(n,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function i(e){let[r,o]=n.useState(a());return(0,l.N)(()=>{e||o(e=>e??String(s++))},[e]),e||(r?`radix-${r}`:"")}},2596:(e,r,o)=>{o.d(r,{$:()=>t});function t(){for(var e,r,o=0,t="",n=arguments.length;o<n;o++)(e=arguments[o])&&(r=function e(r){var o,t,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var l=r.length;for(o=0;o<l;o++)r[o]&&(t=e(r[o]))&&(n&&(n+=" "),n+=t)}else for(t in r)r[t]&&(n&&(n+=" "),n+=t);return n}(e))&&(t&&(t+=" "),t+=r);return t}},2712:(e,r,o)=>{o.d(r,{N:()=>n});var t=o(2115),n=globalThis?.document?t.useLayoutEffect:()=>{}},3655:(e,r,o)=>{o.d(r,{hO:()=>i,sG:()=>s});var t=o(2115),n=o(7650),l=o(9708),a=o(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let o=(0,l.TL)(`Primitive.${r}`),n=t.forwardRef((e,t)=>{let{asChild:n,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?o:r,{...l,ref:t})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{});function i(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},4315:(e,r,o)=>{o.d(r,{jH:()=>l});var t=o(2115);o(5155);var n=t.createContext(void 0);function l(e){let r=t.useContext(n);return e||r||"ltr"}},5185:(e,r,o)=>{o.d(r,{m:()=>t});function t(e,r,{checkForDefaultPrevented:o=!0}={}){return function(t){if(e?.(t),!1===o||!t.defaultPrevented)return r?.(t)}}},5845:(e,r,o)=>{o.d(r,{i:()=>s});var t,n=o(2115),l=o(2712),a=(t||(t=o.t(n,2)))[" useInsertionEffect ".trim().toString()]||l.N;function s({prop:e,defaultProp:r,onChange:o=()=>{},caller:t}){let[l,s,i]=function({defaultProp:e,onChange:r}){let[o,t]=n.useState(e),l=n.useRef(o),s=n.useRef(r);return a(()=>{s.current=r},[r]),n.useEffect(()=>{l.current!==o&&(s.current?.(o),l.current=o)},[o,l]),[o,t,s]}({defaultProp:r,onChange:o}),c=void 0!==e,d=c?e:l;{let r=n.useRef(void 0!==e);n.useEffect(()=>{let e=r.current;if(e!==c){let r=c?"controlled":"uncontrolled";console.warn(`${t} is changing from ${e?"controlled":"uncontrolled"} to ${r}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}r.current=c},[c,t])}return[d,n.useCallback(r=>{if(c){let o="function"==typeof r?r(e):r;o!==e&&i.current?.(o)}else s(r)},[c,e,s,i])]}Symbol("RADIX:SYNC_STATE")},6081:(e,r,o)=>{o.d(r,{A:()=>l});var t=o(2115),n=o(5155);function l(e,r=[]){let o=[],a=()=>{let r=o.map(e=>t.createContext(e));return function(o){let n=o?.[e]||r;return t.useMemo(()=>({[`__scope${e}`]:{...o,[e]:n}}),[o,n])}};return a.scopeName=e,[function(r,l){let a=t.createContext(l),s=o.length;o=[...o,l];let i=r=>{let{scope:o,children:l,...i}=r,c=o?.[e]?.[s]||a,d=t.useMemo(()=>i,Object.values(i));return(0,n.jsx)(c.Provider,{value:d,children:l})};return i.displayName=r+"Provider",[i,function(o,n){let i=n?.[e]?.[s]||a,c=t.useContext(i);if(c)return c;if(void 0!==l)return l;throw Error(`\`${o}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let o=()=>{let o=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=o.reduce((r,{useScope:o,scopeName:t})=>{let n=o(e)[`__scope${t}`];return{...r,...n}},{});return t.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return o.scopeName=r.scopeName,o}(a,...r)]}},6101:(e,r,o)=>{o.d(r,{s:()=>a,t:()=>l});var t=o(2115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let o=!1,t=e.map(e=>{let t=n(e,r);return o||"function"!=typeof t||(o=!0),t});if(o)return()=>{for(let r=0;r<t.length;r++){let o=t[r];"function"==typeof o?o():n(e[r],null)}}}}function a(...e){return t.useCallback(l(...e),e)}},7328:(e,r,o)=>{function t(e,r,o){if(!r.has(e))throw TypeError("attempted to "+o+" private field on non-instance");return r.get(e)}function n(e,r){var o=t(e,r,"get");return o.get?o.get.call(e):o.value}function l(e,r,o){var n=t(e,r,"set");if(n.set)n.set.call(e,o);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=o}return o}o.d(r,{N:()=>m});var a,s=o(2115),i=o(6081),c=o(6101),d=o(9708),u=o(5155);function m(e){let r=e+"CollectionProvider",[o,t]=(0,i.A)(r),[n,l]=o(r,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:r,children:o}=e,t=s.useRef(null),l=s.useRef(new Map).current;return(0,u.jsx)(n,{scope:r,itemMap:l,collectionRef:t,children:o})};a.displayName=r;let m=e+"CollectionSlot",p=(0,d.TL)(m),f=s.forwardRef((e,r)=>{let{scope:o,children:t}=e,n=l(m,o),a=(0,c.s)(r,n.collectionRef);return(0,u.jsx)(p,{ref:a,children:t})});f.displayName=m;let b=e+"CollectionItemSlot",g="data-radix-collection-item",h=(0,d.TL)(b),w=s.forwardRef((e,r)=>{let{scope:o,children:t,...n}=e,a=s.useRef(null),i=(0,c.s)(r,a),d=l(b,o);return s.useEffect(()=>(d.itemMap.set(a,{ref:a,...n}),()=>void d.itemMap.delete(a))),(0,u.jsx)(h,{...{[g]:""},ref:i,children:t})});return w.displayName=b,[{Provider:a,Slot:f,ItemSlot:w},function(r){let o=l(e+"CollectionConsumer",r);return s.useCallback(()=>{let e=o.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(o.itemMap.values()).sort((e,o)=>r.indexOf(e.ref.current)-r.indexOf(o.ref.current))},[o.collectionRef,o.itemMap])},t]}var p=new WeakMap;function f(e,r){if("at"in Array.prototype)return Array.prototype.at.call(e,r);let o=function(e,r){let o=e.length,t=b(r),n=t>=0?t:o+t;return n<0||n>=o?-1:n}(e,r);return -1===o?void 0:e[o]}function b(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},9033:(e,r,o)=>{o.d(r,{c:()=>n});var t=o(2115);function n(e){let r=t.useRef(e);return t.useEffect(()=>{r.current=e}),t.useMemo(()=>(...e)=>r.current?.(...e),[])}},9688:(e,r,o)=>{o.d(r,{QP:()=>ec});let t=e=>{let r=s(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),n(o,r)||a(e)},getConflictingClassGroupIds:(e,r)=>{let n=o[e]||[];return r&&t[e]?[...n,...t[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let o=e[0],t=r.nextPart.get(o),l=t?n(e.slice(1),t):void 0;if(l)return l;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},l=/^\[(.+)\]$/,a=e=>{if(l.test(e)){let r=l.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},s=e=>{let{theme:r,classGroups:o}=e,t={nextPart:new Map,validators:[]};for(let e in o)i(o[e],t,e,r);return t},i=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:c(r,e)).classGroupId=o;return}if("function"==typeof e)return d(e)?void i(e(t),r,o,t):void r.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach(([e,n])=>{i(n,c(r,e),o,t)})})},c=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,n=(n,l)=>{o.set(n,l),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(n(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):n(e,r)}}},m=e=>{let{prefix:r,experimentalParseClassName:o}=e,t=e=>{let r,o=[],t=0,n=0,l=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===t&&0===n){if(":"===s){o.push(e.slice(l,a)),l=a+1;continue}if("/"===s){r=a;continue}}"["===s?t++:"]"===s?t--:"("===s?n++:")"===s&&n--}let a=0===o.length?e:e.substring(l),s=p(a);return{modifiers:o,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",o=t;t=r=>r.startsWith(e)?o(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(o){let e=t;t=r=>o({className:r,parseClassName:e})}return t},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],t=[];return e.forEach(e=>{"["===e[0]||r[e]?(o.push(...t.sort(),e),t=[]):t.push(e)}),o.push(...t.sort()),o}},b=e=>({cache:u(e.cacheSize),parseClassName:m(e),sortModifiers:f(e),...t(e)}),g=/\s+/,h=(e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:n,sortModifiers:l}=r,a=[],s=e.trim().split(g),i="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=o(r);if(c){i=r+(i.length>0?" "+i:i);continue}let f=!!p,b=t(f?m.substring(0,p):m);if(!b){if(!f||!(b=t(m))){i=r+(i.length>0?" "+i:i);continue}f=!1}let g=l(d).join(":"),h=u?g+"!":g,w=h+b;if(a.includes(w))continue;a.push(w);let x=n(b,f);for(let e=0;e<x.length;++e){let r=x[e];a.push(h+r)}i=r+(i.length>0?" "+i:i)}return i};function w(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=x(e))&&(t&&(t+=" "),t+=r);return t}let x=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=x(e[t]))&&(o&&(o+=" "),o+=r);return o},v=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},k=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,y=/^\((?:(\w[\w-]*):)?(.+)\)$/i,z=/^\d+\/\d+$/,j=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,N=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,S=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=e=>z.test(e),R=e=>!!e&&!Number.isNaN(Number(e)),$=e=>!!e&&Number.isInteger(Number(e)),P=e=>e.endsWith("%")&&R(e.slice(0,-1)),_=e=>j.test(e),I=()=>!0,A=e=>C.test(e)&&!N.test(e),G=()=>!1,T=e=>M.test(e),O=e=>S.test(e),W=e=>!L(e)&&!H(e),D=e=>ee(e,en,G),L=e=>k.test(e),V=e=>ee(e,el,A),q=e=>ee(e,ea,R),B=e=>ee(e,eo,G),X=e=>ee(e,et,O),F=e=>ee(e,ei,T),H=e=>y.test(e),Q=e=>er(e,el),Y=e=>er(e,es),Z=e=>er(e,eo),J=e=>er(e,en),K=e=>er(e,et),U=e=>er(e,ei,!0),ee=(e,r,o)=>{let t=k.exec(e);return!!t&&(t[1]?r(t[1]):o(t[2]))},er=(e,r,o=!1)=>{let t=y.exec(e);return!!t&&(t[1]?r(t[1]):o)},eo=e=>"position"===e||"percentage"===e,et=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,ea=e=>"number"===e,es=e=>"family-name"===e,ei=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...r){let o,t,n,l=function(s){return t=(o=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=o.cache.set,l=a,a(s)};function a(e){let r=t(e);if(r)return r;let l=h(e,o);return n(e,l),l}return function(){return l(w.apply(null,arguments))}}(()=>{let e=v("color"),r=v("font"),o=v("text"),t=v("font-weight"),n=v("tracking"),l=v("leading"),a=v("breakpoint"),s=v("container"),i=v("spacing"),c=v("radius"),d=v("shadow"),u=v("inset-shadow"),m=v("text-shadow"),p=v("drop-shadow"),f=v("blur"),b=v("perspective"),g=v("aspect"),h=v("ease"),w=v("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],y=()=>[...k(),H,L],z=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],C=()=>[H,L,i],N=()=>[E,"full","auto",...C()],M=()=>[$,"none","subgrid",H,L],S=()=>["auto",{span:["full",$,H,L]},$,H,L],A=()=>[$,"auto",H,L],G=()=>["auto","min","max","fr",H,L],T=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],er=()=>[E,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],eo=()=>[e,H,L],et=()=>[...k(),Z,B,{position:[H,L]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],el=()=>["auto","cover","contain",J,D,{size:[H,L]}],ea=()=>[P,Q,V],es=()=>["","none","full",c,H,L],ei=()=>["",R,Q,V],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[R,P,Z,B],em=()=>["","none",f,H,L],ep=()=>["none",R,H,L],ef=()=>["none",R,H,L],eb=()=>[R,H,L],eg=()=>[E,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[_],breakpoint:[_],color:[I],container:[_],"drop-shadow":[_],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[_],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[_],shadow:[_],spacing:["px",R],text:[_],"text-shadow":[_],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",E,L,H,g]}],container:["container"],columns:[{columns:[R,L,H,s]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:z()}],"overflow-x":[{"overflow-x":z()}],"overflow-y":[{"overflow-y":z()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:N()}],"inset-x":[{"inset-x":N()}],"inset-y":[{"inset-y":N()}],start:[{start:N()}],end:[{end:N()}],top:[{top:N()}],right:[{right:N()}],bottom:[{bottom:N()}],left:[{left:N()}],visibility:["visible","invisible","collapse"],z:[{z:[$,"auto",H,L]}],basis:[{basis:[E,"full","auto",s,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[R,E,"auto","initial","none",L]}],grow:[{grow:["",R,H,L]}],shrink:[{shrink:["",R,H,L]}],order:[{order:[$,"first","last","none",H,L]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":G()}],"auto-rows":[{"auto-rows":G()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...T(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...T()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":T()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",o,Q,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,H,q]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",P,L]}],"font-family":[{font:[Y,L,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,H,L]}],"line-clamp":[{"line-clamp":[R,"none",H,q]}],leading:[{leading:[l,...C()]}],"list-image":[{"list-image":["none",H,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",H,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:eo()}],"text-color":[{text:eo()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[R,"from-font","auto",H,V]}],"text-decoration-color":[{decoration:eo()}],"underline-offset":[{"underline-offset":[R,"auto",H,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:et()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:el()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},$,H,L],radial:["",H,L],conic:[$,H,L]},K,X]}],"bg-color":[{bg:eo()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:eo()}],"gradient-via":[{via:eo()}],"gradient-to":[{to:eo()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:eo()}],"border-color-x":[{"border-x":eo()}],"border-color-y":[{"border-y":eo()}],"border-color-s":[{"border-s":eo()}],"border-color-e":[{"border-e":eo()}],"border-color-t":[{"border-t":eo()}],"border-color-r":[{"border-r":eo()}],"border-color-b":[{"border-b":eo()}],"border-color-l":[{"border-l":eo()}],"divide-color":[{divide:eo()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[R,H,L]}],"outline-w":[{outline:["",R,Q,V]}],"outline-color":[{outline:eo()}],shadow:[{shadow:["","none",d,U,F]}],"shadow-color":[{shadow:eo()}],"inset-shadow":[{"inset-shadow":["none",u,U,F]}],"inset-shadow-color":[{"inset-shadow":eo()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:eo()}],"ring-offset-w":[{"ring-offset":[R,V]}],"ring-offset-color":[{"ring-offset":eo()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":eo()}],"text-shadow":[{"text-shadow":["none",m,U,F]}],"text-shadow-color":[{"text-shadow":eo()}],opacity:[{opacity:[R,H,L]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[R]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":eo()}],"mask-image-linear-to-color":[{"mask-linear-to":eo()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":eo()}],"mask-image-t-to-color":[{"mask-t-to":eo()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":eo()}],"mask-image-r-to-color":[{"mask-r-to":eo()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":eo()}],"mask-image-b-to-color":[{"mask-b-to":eo()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":eo()}],"mask-image-l-to-color":[{"mask-l-to":eo()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":eo()}],"mask-image-x-to-color":[{"mask-x-to":eo()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":eo()}],"mask-image-y-to-color":[{"mask-y-to":eo()}],"mask-image-radial":[{"mask-radial":[H,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":eo()}],"mask-image-radial-to-color":[{"mask-radial-to":eo()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[R]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":eo()}],"mask-image-conic-to-color":[{"mask-conic-to":eo()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:et()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:el()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",H,L]}],filter:[{filter:["","none",H,L]}],blur:[{blur:em()}],brightness:[{brightness:[R,H,L]}],contrast:[{contrast:[R,H,L]}],"drop-shadow":[{"drop-shadow":["","none",p,U,F]}],"drop-shadow-color":[{"drop-shadow":eo()}],grayscale:[{grayscale:["",R,H,L]}],"hue-rotate":[{"hue-rotate":[R,H,L]}],invert:[{invert:["",R,H,L]}],saturate:[{saturate:[R,H,L]}],sepia:[{sepia:["",R,H,L]}],"backdrop-filter":[{"backdrop-filter":["","none",H,L]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[R,H,L]}],"backdrop-contrast":[{"backdrop-contrast":[R,H,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",R,H,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[R,H,L]}],"backdrop-invert":[{"backdrop-invert":["",R,H,L]}],"backdrop-opacity":[{"backdrop-opacity":[R,H,L]}],"backdrop-saturate":[{"backdrop-saturate":[R,H,L]}],"backdrop-sepia":[{"backdrop-sepia":["",R,H,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",H,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[R,"initial",H,L]}],ease:[{ease:["linear","initial",h,H,L]}],delay:[{delay:[R,H,L]}],animate:[{animate:["none",w,H,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,H,L]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[H,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:eo()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:eo()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H,L]}],fill:[{fill:["none",...eo()]}],"stroke-w":[{stroke:[R,Q,V,q]}],stroke:[{stroke:["none",...eo()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,r,o)=>{o.d(r,{DX:()=>s,TL:()=>a});var t=o(2115),n=o(6101),l=o(5155);function a(e){let r=function(e){let r=t.forwardRef((e,r)=>{let{children:o,...l}=e;if(t.isValidElement(o)){var a;let e,s,i=(a=o,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,r){let o={...r};for(let t in r){let n=e[t],l=r[t];/^on[A-Z]/.test(t)?n&&l?o[t]=(...e)=>{let r=l(...e);return n(...e),r}:n&&(o[t]=n):"style"===t?o[t]={...n,...l}:"className"===t&&(o[t]=[n,l].filter(Boolean).join(" "))}return{...e,...o}}(l,o.props);return o.type!==t.Fragment&&(c.ref=r?(0,n.t)(r,i):i),t.cloneElement(o,c)}return t.Children.count(o)>1?t.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),o=t.forwardRef((e,o)=>{let{children:n,...a}=e,s=t.Children.toArray(n),i=s.find(c);if(i){let e=i.props.children,n=s.map(r=>r!==i?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...a,ref:o,children:t.isValidElement(e)?t.cloneElement(e,void 0,n):null})}return(0,l.jsx)(r,{...a,ref:o,children:n})});return o.displayName=`${e}.Slot`,o}var s=a("Slot"),i=Symbol("radix.slottable");function c(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}}]);