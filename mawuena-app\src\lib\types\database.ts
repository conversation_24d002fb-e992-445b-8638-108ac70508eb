export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          instagram_handle: string | null
          business_name: string | null
          mastra_agent_config: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          instagram_handle?: string | null
          business_name?: string | null
          mastra_agent_config?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          instagram_handle?: string | null
          business_name?: string | null
          mastra_agent_config?: J<PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      clothing_items: {
        Row: {
          id: string
          user_id: string
          name: string
          brand: string | null
          size: string | null
          price: number | null
          images: string[]
          status: 'draft' | 'ready' | 'posted' | 'sold'
          user_description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          brand?: string | null
          size?: string | null
          price?: number | null
          images?: string[]
          status?: 'draft' | 'ready' | 'posted' | 'sold'
          user_description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          brand?: string | null
          size?: string | null
          price?: number | null
          images?: string[]
          status?: 'draft' | 'ready' | 'posted' | 'sold'
          user_description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          user_id: string
          instagram_username: string
          name: string | null
          phone: string | null
          email: string | null
          purchase_history: Json
          ai_profile: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          instagram_username: string
          name?: string | null
          phone?: string | null
          email?: string | null
          purchase_history?: Json
          ai_profile?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          instagram_username?: string
          name?: string | null
          phone?: string | null
          email?: string | null
          purchase_history?: Json
          ai_profile?: Json
          created_at?: string
          updated_at?: string
        }
      }
      instagram_posts: {
        Row: {
          id: string
          item_id: string
          user_id: string
          instagram_post_id: string | null
          caption: string | null
          posted_at: string | null
          engagement_stats: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          item_id: string
          user_id: string
          instagram_post_id?: string | null
          caption?: string | null
          posted_at?: string | null
          engagement_stats?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          item_id?: string
          user_id?: string
          instagram_post_id?: string | null
          caption?: string | null
          posted_at?: string | null
          engagement_stats?: Json
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          customer_id: string
          user_id: string
          messages: Json
          ai_responses: Json
          agent_context: Json
          status: 'active' | 'closed' | 'archived'
          last_activity: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          user_id: string
          messages?: Json
          ai_responses?: Json
          agent_context?: Json
          status?: 'active' | 'closed' | 'archived'
          last_activity?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          user_id?: string
          messages?: Json
          ai_responses?: Json
          agent_context?: Json
          status?: 'active' | 'closed' | 'archived'
          last_activity?: string
          created_at?: string
          updated_at?: string
        }
      }
      sales: {
        Row: {
          id: string
          item_id: string
          customer_id: string
          user_id: string
          amount: number
          status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled'
          trigger_workflow_id: string | null
          payment_reference: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          item_id: string
          customer_id: string
          user_id: string
          amount: number
          status?: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled'
          trigger_workflow_id?: string | null
          payment_reference?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          item_id?: string
          customer_id?: string
          user_id?: string
          amount?: number
          status?: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled'
          trigger_workflow_id?: string | null
          payment_reference?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      automation_rules: {
        Row: {
          id: string
          user_id: string
          name: string
          trigger_type: string
          action_type: string
          conditions: Json
          mastra_tool_config: Json
          active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          trigger_type: string
          action_type: string
          conditions?: Json
          mastra_tool_config?: Json
          active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          trigger_type?: string
          action_type?: string
          conditions?: Json
          mastra_tool_config?: Json
          active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      agent_sessions: {
        Row: {
          id: string
          user_id: string
          agent_name: string
          session_data: Json
          traces: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          agent_name: string
          session_data?: Json
          traces?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          agent_name?: string
          session_data?: Json
          traces?: Json
          created_at?: string
          updated_at?: string
        }
      }
      agent_memory: {
        Row: {
          id: string
          agent_id: string
          customer_id: string | null
          context_data: Json
          embeddings: number[] | null
          updated_at: string
        }
        Insert: {
          id?: string
          agent_id: string
          customer_id?: string | null
          context_data?: Json
          embeddings?: number[] | null
          updated_at?: string
        }
        Update: {
          id?: string
          agent_id?: string
          customer_id?: string | null
          context_data?: Json
          embeddings?: number[] | null
          updated_at?: string
        }
      }
      workflow_executions: {
        Row: {
          id: string
          trigger_job_id: string
          agent_id: string | null
          user_id: string
          status: 'running' | 'completed' | 'failed' | 'cancelled'
          logs: Json
          duration: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          trigger_job_id: string
          agent_id?: string | null
          user_id: string
          status?: 'running' | 'completed' | 'failed' | 'cancelled'
          logs?: Json
          duration?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          trigger_job_id?: string
          agent_id?: string | null
          user_id?: string
          status?: 'running' | 'completed' | 'failed' | 'cancelled'
          logs?: Json
          duration?: number | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_inventory_stats: {
        Args: {
          user_uuid: string
        }
        Returns: {
          total_items: number
          draft_items: number
          ready_items: number
          posted_items: number
          sold_items: number
          total_value: number
        }[]
      }
      get_recent_customer_activity: {
        Args: {
          user_uuid: string
          days_back?: number
        }
        Returns: {
          customer_name: string | null
          instagram_username: string
          last_message_at: string | null
          total_purchases: number
          total_spent: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
