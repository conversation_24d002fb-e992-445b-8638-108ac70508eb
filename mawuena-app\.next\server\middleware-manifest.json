{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "PxJNPfPg7saArgpCnaspl", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "J6iVE/n0uJP501xp0WRPooH+nci7jujvTCOkzis6iNE=", "__NEXT_PREVIEW_MODE_ID": "de64c0c4f629877d965f8dc4f3ec8094", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e8fa491212393f1ac43bfc0440e82e3ca02007ef8a417def2fcdf8bb443272d0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d48bf83d0a36e2369f2e6cad610d5d891475c150e216ea145aec9df324b7ba82"}}}, "functions": {}, "sortedMiddleware": ["/"]}