{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "L9ldNXFQ_LSpYx3ELrdlA", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "J6iVE/n0uJP501xp0WRPooH+nci7jujvTCOkzis6iNE=", "__NEXT_PREVIEW_MODE_ID": "a959e29566ba95a0e7f21e5d372035df", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6f8fd63419719752b5c04e32745b943d9307e9991cf642195a330d066eb5b484", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c2a9b478f57c47f0b367308f10a6ccee27d1b2f0178f9cdac0bce1432ed80066"}}}, "functions": {}, "sortedMiddleware": ["/"]}