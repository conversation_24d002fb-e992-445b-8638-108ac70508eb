{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7ad4c31e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_0e03f682.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "J6iVE/n0uJP501xp0WRPooH+nci7jujvTCOkzis6iNE=", "__NEXT_PREVIEW_MODE_ID": "45e08e3c170f553b93355814e8a699f5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3e4d3f11c225ae02a6b1c80f65af91ca7daa1e2ea99e10fbca30a95d5e6e5be3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8f2135d002cfbd3bd3f8356c77b4792e48520df0e138f413d154d395178fcac0"}}}, "sortedMiddleware": ["/"], "functions": {}}