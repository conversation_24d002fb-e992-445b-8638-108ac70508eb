(()=>{var e={};e.id=47,e.ids=[47],e.modules={163:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=n(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1933:(e,t)=>{"use strict";function n(e){var t;let{config:n,src:i,width:a,quality:r}=e,o=r||(null==(t=n.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return n.path+"?url="+encodeURIComponent(i)+"&w="+a+"&q="+o+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),n.__next_img_default=!0;let i=n},2295:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},2507:(e,t,n)=>{"use strict";n.d(t,{U:()=>r});var i=n(80261),a=n(44999);async function r(){let e=await (0,a.UL)();return(0,i.createServerClient)(process.env.NEXT_PUBLIC_SUPABASE_URL,process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:n,options:i})=>e.set(t,n,i))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,n)=>{"use strict";n.d(t,{cn:()=>r});var i=n(49384),a=n(82348);function r(...e){return(0,a.QP)((0,i.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,n)=>{"use strict";n.d(t,{cn:()=>r});var i=n(75986),a=n(8974);function r(...e){return(0,a.QP)((0,i.$)(e))}},11997:e=>{"use strict";e.exports=require("punycode")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return i}});let n=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},13178:()=>{},14959:(e,t,n)=>{"use strict";e.exports=n(94041).vendored.contexts.AmpContext},17903:(e,t,n)=>{"use strict";e.exports=n(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23469:(e,t,n)=>{"use strict";n.d(t,{$:()=>c});var i=n(37413);n(61120);var a=n(70403),r=n(50662),o=n(10974);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:n,asChild:r=!1,...c}){let s=r?a.DX:"button";return(0,i.jsx)(s,{"data-slot":"button",className:(0,o.cn)(l({variant:t,size:n,className:e})),...c})}},27406:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(0===n.length)return!0;var i=e.name||"",a=(e.type||"").toLowerCase(),r=a.replace(/\/.*$/,"");return n.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?r===t.replace(/\/.*$/,""):a===t})}return!0}},27910:e=>{"use strict";e.exports=require("stream")},29280:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});let i=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Fluxitude\\\\Projects\\\\human-ai-collaboration-framework\\\\mawuena-app\\\\src\\\\components\\\\inventory\\\\BulkUploadForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\inventory\\BulkUploadForm.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30503:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,4536,23)),Promise.resolve().then(n.bind(n,29280))},30512:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},defaultHead:function(){return d}});let i=n(14985),a=n(40740),r=n(60687),o=a._(n(43210)),l=i._(n(47755)),c=n(14959),s=n(89513),p=n(34604);function d(e){void 0===e&&(e=!1);let t=[(0,r.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,r.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function u(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(50148);let m=["name","httpEquiv","charSet","itemProp"];function f(e,t){let{inAmpMode:n}=t;return e.reduce(u,[]).reverse().concat(d(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,i={};return a=>{let r=!0,o=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){o=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?r=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?r=!1:t.add(a.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(a.props.hasOwnProperty(t))if("charSet"===t)n.has(t)?r=!1:n.add(t);else{let e=a.props[t],n=i[t]||new Set;("name"!==t||!o)&&n.has(e)?r=!1:(n.add(e),i[t]=n)}}}return r}}()).reverse().map((e,t)=>{let i=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:i})})}let v=function(e){let{children:t}=e,n=(0,o.useContext)(c.AmpStateContext),i=(0,o.useContext)(s.HeadManagerContext);return(0,r.jsx)(l.default,{reduceComponentsToState:f,headManager:i,inAmpMode:(0,p.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31261:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return c},getImageProps:function(){return l}});let i=n(14985),a=n(44953),r=n(46533),o=i._(n(1933));function l(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let c=r.Image},33873:e=>{"use strict";e.exports=require("path")},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34604:(e,t)=>{"use strict";function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||n&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},39916:(e,t,n)=>{"use strict";var i=n(97576);n.o(i,"redirect")&&n.d(t,{redirect:function(){return i.redirect}})},41480:(e,t)=>{"use strict";function n(e){let{widthInt:t,heightInt:n,blurWidth:i,blurHeight:a,blurDataURL:r,objectFit:o}=e,l=i?40*i:t,c=a?40*a:n,s=l&&c?"viewBox='0 0 "+l+" "+c+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+r+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},44953:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return c}}),n(50148);let i=n(41480),a=n(12756),r=["-moz-initial","fill","none","scale-down",void 0];function o(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function c(e,t){var n,c;let s,p,d,{src:u,sizes:m,unoptimized:f=!1,priority:v=!1,loading:x,className:g,quality:h,width:b,height:y,fill:w=!1,style:j,overrideSrc:k,onLoad:E,onLoadingComplete:S,placeholder:P="empty",blurDataURL:R,fetchPriority:C,decoding:O="async",layout:_,objectFit:z,objectPosition:A,lazyBoundary:D,lazyRoot:N,...T}=e,{imgConf:M,showAltText:L,blurComplete:F,defaultLoader:I}=t,q=M||a.imageConfigDefault;if("allSizes"in q)s=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),i=null==(n=q.qualities)?void 0:n.sort((e,t)=>e-t);s={...q,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===I)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=T.loader||I;delete T.loader,delete T.srcSet;let H="__next_img_default"in B;if(H){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:n,...i}=t;return e(i)}}if(_){"fill"===_&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[_];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[_];t&&!m&&(m=t)}let W="",U=l(b),$=l(y);if((c=u)&&"object"==typeof c&&(o(c)||void 0!==c.src)){let e=o(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(p=e.blurWidth,d=e.blurHeight,R=R||e.blurDataURL,W=e.src,!w)if(U||$){if(U&&!$){let t=U/e.width;$=Math.round(e.height*t)}else if(!U&&$){let t=$/e.height;U=Math.round(e.width*t)}}else U=e.width,$=e.height}let G=!v&&("lazy"===x||void 0===x);(!(u="string"==typeof u?u:W)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,G=!1),s.unoptimized&&(f=!0),H&&!s.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(f=!0);let V=l(h),X=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:A}:{},L?{}:{color:"transparent"},j),K=F||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:U,heightInt:$,blurWidth:p,blurHeight:d,blurDataURL:R||"",objectFit:X.objectFit})+'")':'url("'+P+'")',Y=r.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=K?{backgroundSize:Y,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},J=function(e){let{config:t,src:n,unoptimized:i,width:a,quality:r,sizes:o,loader:l}=e;if(i)return{src:n,srcSet:void 0,sizes:void 0};let{widths:c,kind:s}=function(e,t,n){let{deviceSizes:i,allSizes:a}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(n);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:a,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))],kind:"x"}}(t,a,o),p=c.length-1;return{sizes:o||"w"!==s?o:"100vw",srcSet:c.map((e,i)=>l({config:t,src:n,quality:r,width:e})+" "+("w"===s?e:i+1)+s).join(", "),src:l({config:t,src:n,quality:r,width:c[p]})}}({config:s,src:u,unoptimized:f,width:U,quality:V,sizes:m,loader:B});return{props:{...T,loading:G?"lazy":x,fetchPriority:C,width:U,height:$,decoding:O,className:g,style:{...X,...Z},sizes:J.sizes,srcSet:J.srcSet,src:k||J.src},meta:{unoptimized:f,priority:v,placeholder:P,fill:w}}}},46533:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let i=n(14985),a=n(40740),r=n(60687),o=a._(n(43210)),l=i._(n(51215)),c=i._(n(30512)),s=n(44953),p=n(12756),d=n(17903);n(50148);let u=n(69148),m=i._(n(1933)),f=n(53038),v={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,n,i,a,r,o){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,a=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function g(e){return o.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let h=(0,o.forwardRef)((e,t)=>{let{src:n,srcSet:i,sizes:a,height:l,width:c,decoding:s,className:p,style:d,fetchPriority:u,placeholder:m,loading:v,unoptimized:h,fill:b,onLoadRef:y,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:k,sizesInput:E,onLoad:S,onError:P,...R}=e,C=(0,o.useCallback)(e=>{e&&(P&&(e.src=e.src),e.complete&&x(e,m,y,w,j,h,E))},[n,m,y,w,j,P,h,E]),O=(0,f.useMergedRef)(t,C);return(0,r.jsx)("img",{...R,...g(u),loading:v,width:c,height:l,decoding:s,"data-nimg":b?"fill":"1",className:p,style:d,sizes:a,srcSet:i,src:n,ref:O,onLoad:e=>{x(e.currentTarget,m,y,w,j,h,E)},onError:e=>{k(!0),"empty"!==m&&j(!0),P&&P(e)}})});function b(e){let{isAppRouter:t,imgAttributes:n}=e,i={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...g(n.fetchPriority)};return t&&l.default.preload?(l.default.preload(n.src,i),null):(0,r.jsx)(c.default,{children:(0,r.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...i},"__nimg-"+n.src+n.srcSet+n.sizes)})}let y=(0,o.forwardRef)((e,t)=>{let n=(0,o.useContext)(u.RouterContext),i=(0,o.useContext)(d.ImageConfigContext),a=(0,o.useMemo)(()=>{var e;let t=v||i||p.imageConfigDefault,n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),a=t.deviceSizes.sort((e,t)=>e-t),r=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:a,qualities:r}},[i]),{onLoad:l,onLoadingComplete:c}=e,f=(0,o.useRef)(l);(0,o.useEffect)(()=>{f.current=l},[l]);let x=(0,o.useRef)(c);(0,o.useEffect)(()=>{x.current=c},[c]);let[g,y]=(0,o.useState)(!1),[w,j]=(0,o.useState)(!1),{props:k,meta:E}=(0,s.getImgProps)(e,{defaultLoader:m.default,imgConf:a,blurComplete:g,showAltText:w});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h,{...k,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:f,onLoadingCompleteRef:x,setBlurComplete:y,setShowAltText:j,sizesInput:e.sizes,ref:t}),E.priority?(0,r.jsx)(b,{isAppRouter:!n,imgAttributes:k}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let i=n(43210),a=()=>{},r=()=>{};function o(e){var t;let{headManager:n,reduceComponentsToState:o}=e;function l(){if(n&&n.mountedInstances){let t=i.Children.toArray(Array.from(n.mountedInstances).filter(Boolean));n.updateHead(o(t,e))}}return null==n||null==(t=n.mountedInstances)||t.add(e.children),l(),a(()=>{var t;return null==n||null==(t=n.mountedInstances)||t.add(e.children),()=>{var t;null==n||null==(t=n.mountedInstances)||t.delete(e.children)}}),a(()=>(n&&(n._pendingUpdate=l),()=>{n&&(n._pendingUpdate=l)})),r(()=>(n&&n._pendingUpdate&&(n._pendingUpdate(),n._pendingUpdate=null),()=>{n&&n._pendingUpdate&&(n._pendingUpdate(),n._pendingUpdate=null)})),null}},47990:()=>{},48976:(e,t,n)=>{"use strict";function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},53346:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60767:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,85814,23)),Promise.resolve().then(n.bind(n,79253))},61135:()=>{},62247:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var i=n(37413),a=n(39916),r=n(2507),o=n(29280),l=n(23469);let c=(0,n(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var s=n(4536),p=n.n(s);async function d(){let e=await (0,r.U)(),{data:t,error:n}=await e.auth.getUser();return(n||!t?.user)&&(0,a.redirect)("/login"),(0,i.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,i.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,i.jsx)(l.$,{variant:"outline",size:"sm",asChild:!0,children:(0,i.jsxs)(p(),{href:"/dashboard/inventory",children:[(0,i.jsx)(c,{className:"h-4 w-4 mr-2"}),"Back to Inventory"]})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Bulk Upload Items"}),(0,i.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Upload multiple images and fill in details using our spreadsheet interface"})]})]}),(0,i.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,i.jsx)(o.default,{userId:t.user.id})})]})})}},62765:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let i=""+n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65343:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))},69148:(e,t,n)=>{"use strict";e.exports=n(94041).vendored.contexts.RouterContext},69699:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>s});var i=n(65239),a=n(48088),r=n(88170),o=n.n(r),l=n(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);n.d(t,c);let s={children:["",{children:["dashboard",{children:["inventory",{children:["bulk-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,62247)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\bulk-upload\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\bulk-upload\\page.tsx"],d={require:n,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/inventory/bulk-upload/page",pathname:"/dashboard/inventory/bulk-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},70440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var i=n(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70899:(e,t,n)=>{"use strict";function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),n(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,r.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,l.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=n(68388),a=n(52637),r=n(51846),o=n(31162),l=n(84971),c=n(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},79253:(e,t,n)=>{"use strict";n.d(t,{default:()=>ah});var i,a,r,o=n(60687),l=n(43210),c=n(87955),s=function(){return(s=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function p(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>t.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(n[i[a]]=e[i[a]]);return n}function d(e,t,n,i){return new(n||(n=Promise))(function(a,r){function o(e){try{c(i.next(e))}catch(e){r(e)}}function l(e){try{c(i.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?a(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,l)}c((i=i.apply(e,t||[])).next())})}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;let u=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function m(e,t,n){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let n=t.split(".").pop().toLowerCase(),i=u.get(n);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:a}=e,r="string"==typeof t?t:"string"==typeof a&&a.length>0?a:`./${e.name}`;return"string"!=typeof i.path&&f(i,"path",r),void 0!==n&&Object.defineProperty(i,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),f(i,"relativePath",r),i}function f(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}let v=[".DS_Store","Thumbs.db"];function x(e){return"object"==typeof e&&null!==e}function g(e){return e.filter(e=>-1===v.indexOf(e.name))}function h(e){if(null===e)return[];let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i)}return t}function b(e){if("function"!=typeof e.webkitGetAsEntry)return y(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?j(t):y(e,t)}function y(e,t){return d(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,m(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return m(i,null!=(n=null==t?void 0:t.fullPath)?n:void 0)})}function w(e){return d(this,void 0,void 0,function*(){return e.isDirectory?j(e):function(e){return d(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(n=>{t(m(n,e.fullPath))},e=>{n(e)})})})}(e)})}function j(e){let t=e.createReader();return new Promise((e,n)=>{let i=[];!function a(){t.readEntries(t=>d(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(w));i.push(e),a()}else try{let t=yield Promise.all(i);e(t)}catch(e){n(e)}}),e=>{n(e)})}()})}var k=n(27406);function E(e){return function(e){if(Array.isArray(e))return _(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||O(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach(function(t){R(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function R(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r=[],o=!0,l=!1;try{for(a=a.call(e);!(o=(n=a.next()).done)&&(r.push(n.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==a.return||a.return()}finally{if(l)throw i}}return r}}(e,t)||O(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){if(e){if("string"==typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(e,t)}}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}var z="function"==typeof k?k:k.default,A=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),n=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(n)}},D=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},N=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},T={code:"too-many-files",message:"Too many files"};function M(e,t){var n="application/x-moz-file"===e.type||z(e,t);return[n,n?null:A(t)]}function L(e,t,n){if(F(e.size)){if(F(t)&&F(n)){if(e.size>n)return[!1,D(n)];if(e.size<t)return[!1,N(t)]}else if(F(t)&&e.size<t)return[!1,N(t)];else if(F(n)&&e.size>n)return[!1,D(n)]}return[!0,null]}function F(e){return null!=e}function I(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function q(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function B(e){e.preventDefault()}function H(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,i=Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.some(function(t){return!I(e)&&t&&t.apply(void 0,[e].concat(i)),I(e)})}}function W(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function U(e){return/^.*\.[\w]+$/.test(e)}var $=["children"],G=["open"],V=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],X=["refKey","onChange","onClick"];function K(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r=[],o=!0,l=!1;try{for(a=a.call(e);!(o=(n=a.next()).done)&&(r.push(n.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==a.return||a.return()}finally{if(l)throw i}}return r}}(e,t)||Y(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Y(e,t){if(e){if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Z(e,t)}}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function J(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function Q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?J(Object(n),!0).forEach(function(t){ee(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):J(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function et(e,t){if(null==e)return{};var n,i,a=function(e,t){if(null==e)return{};var n,i,a={},r=Object.keys(e);for(i=0;i<r.length;i++)n=r[i],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(i=0;i<r.length;i++)n=r[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var en=(0,l.forwardRef)(function(e,t){var n=e.children,i=er(et(e,$)),a=i.open,r=et(i,G);return(0,l.useImperativeHandle)(t,function(){return{open:a}},[a]),l.createElement(l.Fragment,null,n(Q(Q({},r),{},{open:a})))});en.displayName="Dropzone";var ei={disabled:!1,getFilesFromEvent:function(e){return d(this,void 0,void 0,function*(){var t;if(x(e)&&x(e.dataTransfer))return function(e,t){return d(this,void 0,void 0,function*(){if(e.items){let n=h(e.items).filter(e=>"file"===e.kind);return"drop"!==t?n:g(function e(t){return t.reduce((t,n)=>[...t,...Array.isArray(n)?e(n):[n]],[])}((yield Promise.all(n.map(b)))))}return g(h(e.files).map(e=>m(e)))})}(e.dataTransfer,e.type);if(x(t=e)&&x(t.target))return h(e.target.files).map(e=>m(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return d(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>m(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};en.defaultProps=ei,en.propTypes={children:c.func,accept:c.objectOf(c.arrayOf(c.string)),multiple:c.bool,preventDropOnDocument:c.bool,noClick:c.bool,noKeyboard:c.bool,noDrag:c.bool,noDragEventsBubbling:c.bool,minSize:c.number,maxSize:c.number,maxFiles:c.number,disabled:c.bool,getFilesFromEvent:c.func,onFileDialogCancel:c.func,onFileDialogOpen:c.func,useFsAccessApi:c.bool,autoFocus:c.bool,onDragEnter:c.func,onDragLeave:c.func,onDragOver:c.func,onDrop:c.func,onDropAccepted:c.func,onDropRejected:c.func,onError:c.func,validator:c.func};var ea={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function er(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Q(Q({},ei),e),n=t.accept,i=t.disabled,a=t.getFilesFromEvent,r=t.maxSize,o=t.minSize,c=t.multiple,s=t.maxFiles,p=t.onDragEnter,d=t.onDragLeave,u=t.onDragOver,m=t.onDrop,f=t.onDropAccepted,v=t.onDropRejected,x=t.onFileDialogCancel,g=t.onFileDialogOpen,h=t.useFsAccessApi,b=t.autoFocus,y=t.preventDropOnDocument,w=t.noClick,j=t.noKeyboard,k=t.noDrag,S=t.noDragEventsBubbling,O=t.onError,_=t.validator,z=(0,l.useMemo)(function(){return F(n)?Object.entries(n).reduce(function(e,t){var n=C(t,2),i=n[0],a=n[1];return[].concat(E(e),[i],E(a))},[]).filter(function(e){return W(e)||U(e)}).join(","):void 0},[n]),A=(0,l.useMemo)(function(){return F(n)?[{description:"Files",accept:Object.entries(n).filter(function(e){var t=C(e,2),n=t[0],i=t[1],a=!0;return W(n)||(console.warn('Skipped "'.concat(n,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),a=!1),Array.isArray(i)&&i.every(U)||(console.warn('Skipped "'.concat(n,'" because an invalid file extension was provided.')),a=!1),a}).reduce(function(e,t){var n=C(t,2),i=n[0],a=n[1];return P(P({},e),{},R({},i,a))},{})}]:n},[n]),D=(0,l.useMemo)(function(){return"function"==typeof g?g:el},[g]),N=(0,l.useMemo)(function(){return"function"==typeof x?x:el},[x]),$=(0,l.useRef)(null),G=(0,l.useRef)(null),J=K((0,l.useReducer)(eo,ea),2),en=J[0],er=J[1],ec=en.isFocused,es=en.isFileDialogActive,ep=(0,l.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),ed=function(){!ep.current&&es&&setTimeout(function(){G.current&&(G.current.files.length||(er({type:"closeDialog"}),N()))},300)};(0,l.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[G,es,N,ep]);var eu=(0,l.useRef)([]),em=function(e){$.current&&$.current.contains(e.target)||(e.preventDefault(),eu.current=[])};(0,l.useEffect)(function(){return y&&(document.addEventListener("dragover",B,!1),document.addEventListener("drop",em,!1)),function(){y&&(document.removeEventListener("dragover",B),document.removeEventListener("drop",em))}},[$,y]),(0,l.useEffect)(function(){return!i&&b&&$.current&&$.current.focus(),function(){}},[$,b,i]);var ef=(0,l.useCallback)(function(e){O?O(e):console.error(e)},[O]),ev=(0,l.useCallback)(function(e){var t;e.preventDefault(),e.persist(),eC(e),eu.current=[].concat(function(e){if(Array.isArray(e))return Z(e)}(t=eu.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||Y(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),q(e)&&Promise.resolve(a(e)).then(function(t){if(!I(e)||S){var n,i,a,l,d,u,m,f,v=t.length,x=v>0&&(i=(n={files:t,accept:z,minSize:o,maxSize:r,multiple:c,maxFiles:s,validator:_}).files,a=n.accept,l=n.minSize,d=n.maxSize,u=n.multiple,m=n.maxFiles,f=n.validator,(!!u||!(i.length>1))&&(!u||!(m>=1)||!(i.length>m))&&i.every(function(e){var t=C(M(e,a),1)[0],n=C(L(e,l,d),1)[0],i=f?f(e):null;return t&&n&&!i}));er({isDragAccept:x,isDragReject:v>0&&!x,isDragActive:!0,type:"setDraggedFiles"}),p&&p(e)}}).catch(function(e){return ef(e)})},[a,p,ef,S,z,o,r,c,s,_]),ex=(0,l.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e);var t=q(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&u&&u(e),!1},[u,S]),eg=(0,l.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e);var t=eu.current.filter(function(e){return $.current&&$.current.contains(e)}),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),eu.current=t,!(t.length>0)&&(er({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),q(e)&&d&&d(e))},[$,d,S]),eh=(0,l.useCallback)(function(e,t){var n=[],i=[];e.forEach(function(e){var t=K(M(e,z),2),a=t[0],l=t[1],c=K(L(e,o,r),2),s=c[0],p=c[1],d=_?_(e):null;if(a&&s&&!d)n.push(e);else{var u=[l,p];d&&(u=u.concat(d)),i.push({file:e,errors:u.filter(function(e){return e})})}}),(!c&&n.length>1||c&&s>=1&&n.length>s)&&(n.forEach(function(e){i.push({file:e,errors:[T]})}),n.splice(0)),er({acceptedFiles:n,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),m&&m(n,i,t),i.length>0&&v&&v(i,t),n.length>0&&f&&f(n,t)},[er,c,z,o,r,s,m,f,v,_]),eb=(0,l.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e),eu.current=[],q(e)&&Promise.resolve(a(e)).then(function(t){(!I(e)||S)&&eh(t,e)}).catch(function(e){return ef(e)}),er({type:"reset"})},[a,eh,ef,S]),ey=(0,l.useCallback)(function(){if(ep.current){er({type:"openDialog"}),D(),window.showOpenFilePicker({multiple:c,types:A}).then(function(e){return a(e)}).then(function(e){eh(e,null),er({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(N(e),er({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(ep.current=!1,G.current?(G.current.value=null,G.current.click()):ef(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ef(e)});return}G.current&&(er({type:"openDialog"}),D(),G.current.value=null,G.current.click())},[er,D,N,h,eh,ef,A,c]),ew=(0,l.useCallback)(function(e){$.current&&$.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ey())},[$,ey]),ej=(0,l.useCallback)(function(){er({type:"focus"})},[]),ek=(0,l.useCallback)(function(){er({type:"blur"})},[]),eE=(0,l.useCallback)(function(){w||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ey,0):ey())},[w,ey]),eS=function(e){return i?null:e},eP=function(e){return j?null:eS(e)},eR=function(e){return k?null:eS(e)},eC=function(e){S&&e.stopPropagation()},eO=(0,l.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.role,a=e.onKeyDown,r=e.onFocus,o=e.onBlur,l=e.onClick,c=e.onDragEnter,s=e.onDragOver,p=e.onDragLeave,d=e.onDrop,u=et(e,V);return Q(Q(ee({onKeyDown:eP(H(a,ew)),onFocus:eP(H(r,ej)),onBlur:eP(H(o,ek)),onClick:eS(H(l,eE)),onDragEnter:eR(H(c,ev)),onDragOver:eR(H(s,ex)),onDragLeave:eR(H(p,eg)),onDrop:eR(H(d,eb)),role:"string"==typeof n&&""!==n?n:"presentation"},void 0===t?"ref":t,$),i||j?{}:{tabIndex:0}),u)}},[$,ew,ej,ek,eE,ev,ex,eg,eb,j,k,i]),e_=(0,l.useCallback)(function(e){e.stopPropagation()},[]),ez=(0,l.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.onChange,i=e.onClick,a=et(e,X);return Q(Q({},ee({accept:z,multiple:c,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eS(H(n,eb)),onClick:eS(H(i,e_)),tabIndex:-1},void 0===t?"ref":t,G)),a)}},[G,n,c,eb,i]);return Q(Q({},en),{},{isFocused:ec&&!i,getRootProps:eO,getInputProps:ez,rootRef:$,inputRef:G,open:eS(ey)})}function eo(e,t){switch(t.type){case"focus":return Q(Q({},e),{},{isFocused:!0});case"blur":return Q(Q({},e),{},{isFocused:!1});case"openDialog":return Q(Q({},ea),{},{isFileDialogActive:!0});case"closeDialog":return Q(Q({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return Q(Q({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return Q(Q({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return Q({},ea);default:return e}}function el(){}var ec=n(8730),es=n(49384);let ep=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,ed=es.$,eu=(e,t)=>n=>{var i;if((null==t?void 0:t.variants)==null)return ed(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:r}=t,o=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],i=null==r?void 0:r[e];if(null===t)return null;let o=ep(t)||ep(i);return a[e][o]}),l=n&&Object.entries(n).reduce((e,t)=>{let[n,i]=t;return void 0===i||(e[n]=i),e},{});return ed(e,o,null==t||null==(i=t.compoundVariants)?void 0:i.reduce((e,t)=>{let{class:n,className:i,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...r,...l}[t]):({...r,...l})[t]===n})?[...e,n,i]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)};var em=n(4780);let ef=eu("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ev({className:e,variant:t,size:n,asChild:i=!1,...a}){let r=i?ec.DX:"button";return(0,o.jsx)(r,{"data-slot":"button",className:(0,em.cn)(ef({variant:t,size:n,className:e})),...a})}function ex({className:e,type:t,...n}){return(0,o.jsx)("input",{type:t,"data-slot":"input",className:(0,em.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}var eg=n(80013);function eh({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card",className:(0,em.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function eb({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-header",className:(0,em.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function ey({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-title",className:(0,em.cn)("leading-none font-semibold",e),...t})}function ew({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-description",className:(0,em.cn)("text-muted-foreground text-sm",e),...t})}function ej({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-content",className:(0,em.cn)("px-6",e),...t})}var ek=n(51215);function eE(e,[t,n]){return Math.min(n,Math.max(t,e))}var eS=n(70569),eP=n(9510),eR=n(98599),eC=n(11273),eO=n(43),e_=n(14163),ez=n(13495),eA="dismissableLayer.update",eD=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),eN=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:i,onPointerDownOutside:r,onFocusOutside:c,onInteractOutside:s,onDismiss:p,...d}=e,u=l.useContext(eD),[m,f]=l.useState(null),v=m?.ownerDocument??globalThis?.document,[,x]=l.useState({}),g=(0,eR.s)(t,e=>f(e)),h=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=h.indexOf(b),w=m?h.indexOf(m):-1,j=u.layersWithOutsidePointerEventsDisabled.size>0,k=w>=y,E=function(e,t=globalThis?.document){let n=(0,ez.c)(e),i=l.useRef(!1),a=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let i=function(){eM("dismissableLayer.pointerDownOutside",n,r,{discrete:!0})},r={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=i,t.addEventListener("click",a.current,{once:!0})):i()}else t.removeEventListener("click",a.current);i.current=!1},r=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(r),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...u.branches].some(e=>e.contains(t));k&&!n&&(r?.(e),s?.(e),e.defaultPrevented||p?.())},v),S=function(e,t=globalThis?.document){let n=(0,ez.c)(e),i=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!i.current&&eM("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...u.branches].some(e=>e.contains(t))&&(c?.(e),s?.(e),e.defaultPrevented||p?.())},v);return!function(e,t=globalThis?.document){let n=(0,ez.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{w===u.layers.size-1&&(i?.(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},v),l.useEffect(()=>{if(m)return n&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(a=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(m)),u.layers.add(m),eT(),()=>{n&&1===u.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=a)}},[m,v,n,u]),l.useEffect(()=>()=>{m&&(u.layers.delete(m),u.layersWithOutsidePointerEventsDisabled.delete(m),eT())},[m,u]),l.useEffect(()=>{let e=()=>x({});return document.addEventListener(eA,e),()=>document.removeEventListener(eA,e)},[]),(0,o.jsx)(e_.sG.div,{...d,ref:g,style:{pointerEvents:j?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,eS.m)(e.onFocusCapture,S.onFocusCapture),onBlurCapture:(0,eS.m)(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:(0,eS.m)(e.onPointerDownCapture,E.onPointerDownCapture)})});function eT(){let e=new CustomEvent(eA);document.dispatchEvent(e)}function eM(e,t,n,{discrete:i}){let a=n.originalEvent.target,r=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),i?(0,e_.hO)(a,r):a.dispatchEvent(r)}eN.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(eD),i=l.useRef(null),a=(0,eR.s)(t,i);return l.useEffect(()=>{let e=i.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,o.jsx)(e_.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch";var eL=0;function eF(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var eI="focusScope.autoFocusOnMount",eq="focusScope.autoFocusOnUnmount",eB={bubbles:!1,cancelable:!0},eH=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:r,...c}=e,[s,p]=l.useState(null),d=(0,ez.c)(a),u=(0,ez.c)(r),m=l.useRef(null),f=(0,eR.s)(t,e=>p(e)),v=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(i){let e=function(e){if(v.paused||!s)return;let t=e.target;s.contains(t)?m.current=t:e$(m.current,{select:!0})},t=function(e){if(v.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||e$(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&e$(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[i,s,v.paused]),l.useEffect(()=>{if(s){eG.add(v);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(eI,eB);s.addEventListener(eI,d),s.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let i of e)if(e$(i,{select:t}),document.activeElement!==n)return}(eW(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&e$(s))}return()=>{s.removeEventListener(eI,d),setTimeout(()=>{let t=new CustomEvent(eq,eB);s.addEventListener(eq,u),s.dispatchEvent(t),t.defaultPrevented||e$(e??document.body,{select:!0}),s.removeEventListener(eq,u),eG.remove(v)},0)}}},[s,d,u,v]);let x=l.useCallback(e=>{if(!n&&!i||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[i,r]=function(e){let t=eW(e);return[eU(t,e),eU(t.reverse(),e)]}(t);i&&r?e.shiftKey||a!==r?e.shiftKey&&a===i&&(e.preventDefault(),n&&e$(r,{select:!0})):(e.preventDefault(),n&&e$(i,{select:!0})):a===t&&e.preventDefault()}},[n,i,v.paused]);return(0,o.jsx)(e_.sG.div,{tabIndex:-1,...c,ref:f,onKeyDown:x})});function eW(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function eU(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function e$(e,{select:t=!1}={}){if(e&&e.focus){var n;let i=document.activeElement;e.focus({preventScroll:!0}),e!==i&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}eH.displayName="FocusScope";var eG=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=eV(e,t)).unshift(t)},remove(t){e=eV(e,t),e[0]?.resume()}}}();function eV(e,t){let n=[...e],i=n.indexOf(t);return -1!==i&&n.splice(i,1),n}var eX=n(96963);let eK=["top","right","bottom","left"],eY=Math.min,eZ=Math.max,eJ=Math.round,eQ=Math.floor,e0=e=>({x:e,y:e}),e1={left:"right",right:"left",bottom:"top",top:"bottom"},e2={start:"end",end:"start"};function e3(e,t){return"function"==typeof e?e(t):e}function e4(e){return e.split("-")[0]}function e6(e){return e.split("-")[1]}function e5(e){return"x"===e?"y":"x"}function e8(e){return"y"===e?"height":"width"}function e9(e){return["top","bottom"].includes(e4(e))?"y":"x"}function e7(e){return e.replace(/start|end/g,e=>e2[e])}function te(e){return e.replace(/left|right|bottom|top/g,e=>e1[e])}function tt(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function tn(e){let{x:t,y:n,width:i,height:a}=e;return{width:i,height:a,top:n,left:t,right:t+i,bottom:n+a,x:t,y:n}}function ti(e,t,n){let i,{reference:a,floating:r}=e,o=e9(t),l=e5(e9(t)),c=e8(l),s=e4(t),p="y"===o,d=a.x+a.width/2-r.width/2,u=a.y+a.height/2-r.height/2,m=a[c]/2-r[c]/2;switch(s){case"top":i={x:d,y:a.y-r.height};break;case"bottom":i={x:d,y:a.y+a.height};break;case"right":i={x:a.x+a.width,y:u};break;case"left":i={x:a.x-r.width,y:u};break;default:i={x:a.x,y:a.y}}switch(e6(t)){case"start":i[l]-=m*(n&&p?-1:1);break;case"end":i[l]+=m*(n&&p?-1:1)}return i}let ta=async(e,t,n)=>{let{placement:i="bottom",strategy:a="absolute",middleware:r=[],platform:o}=n,l=r.filter(Boolean),c=await (null==o.isRTL?void 0:o.isRTL(t)),s=await o.getElementRects({reference:e,floating:t,strategy:a}),{x:p,y:d}=ti(s,i,c),u=i,m={},f=0;for(let n=0;n<l.length;n++){let{name:r,fn:v}=l[n],{x:x,y:g,data:h,reset:b}=await v({x:p,y:d,initialPlacement:i,placement:u,strategy:a,middlewareData:m,rects:s,platform:o,elements:{reference:e,floating:t}});p=null!=x?x:p,d=null!=g?g:d,m={...m,[r]:{...m[r],...h}},b&&f<=50&&(f++,"object"==typeof b&&(b.placement&&(u=b.placement),b.rects&&(s=!0===b.rects?await o.getElementRects({reference:e,floating:t,strategy:a}):b.rects),{x:p,y:d}=ti(s,u,c)),n=-1)}return{x:p,y:d,placement:u,strategy:a,middlewareData:m}};async function tr(e,t){var n;void 0===t&&(t={});let{x:i,y:a,platform:r,rects:o,elements:l,strategy:c}=e,{boundary:s="clippingAncestors",rootBoundary:p="viewport",elementContext:d="floating",altBoundary:u=!1,padding:m=0}=e3(t,e),f=tt(m),v=l[u?"floating"===d?"reference":"floating":d],x=tn(await r.getClippingRect({element:null==(n=await (null==r.isElement?void 0:r.isElement(v)))||n?v:v.contextElement||await (null==r.getDocumentElement?void 0:r.getDocumentElement(l.floating)),boundary:s,rootBoundary:p,strategy:c})),g="floating"===d?{x:i,y:a,width:o.floating.width,height:o.floating.height}:o.reference,h=await (null==r.getOffsetParent?void 0:r.getOffsetParent(l.floating)),b=await (null==r.isElement?void 0:r.isElement(h))&&await (null==r.getScale?void 0:r.getScale(h))||{x:1,y:1},y=tn(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:h,strategy:c}):g);return{top:(x.top-y.top+f.top)/b.y,bottom:(y.bottom-x.bottom+f.bottom)/b.y,left:(x.left-y.left+f.left)/b.x,right:(y.right-x.right+f.right)/b.x}}function to(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function tl(e){return eK.some(t=>e[t]>=0)}async function tc(e,t){let{placement:n,platform:i,elements:a}=e,r=await (null==i.isRTL?void 0:i.isRTL(a.floating)),o=e4(n),l=e6(n),c="y"===e9(n),s=["left","top"].includes(o)?-1:1,p=r&&c?-1:1,d=e3(t,e),{mainAxis:u,crossAxis:m,alignmentAxis:f}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof f&&(m="end"===l?-1*f:f),c?{x:m*p,y:u*s}:{x:u*s,y:m*p}}function ts(){return"undefined"!=typeof window}function tp(e){return tm(e)?(e.nodeName||"").toLowerCase():"#document"}function td(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function tu(e){var t;return null==(t=(tm(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function tm(e){return!!ts()&&(e instanceof Node||e instanceof td(e).Node)}function tf(e){return!!ts()&&(e instanceof Element||e instanceof td(e).Element)}function tv(e){return!!ts()&&(e instanceof HTMLElement||e instanceof td(e).HTMLElement)}function tx(e){return!!ts()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof td(e).ShadowRoot)}function tg(e){let{overflow:t,overflowX:n,overflowY:i,display:a}=tj(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(a)}function th(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function tb(e){let t=ty(),n=tf(e)?tj(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ty(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function tw(e){return["html","body","#document"].includes(tp(e))}function tj(e){return td(e).getComputedStyle(e)}function tk(e){return tf(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function tE(e){if("html"===tp(e))return e;let t=e.assignedSlot||e.parentNode||tx(e)&&e.host||tu(e);return tx(t)?t.host:t}function tS(e,t,n){var i;void 0===t&&(t=[]),void 0===n&&(n=!0);let a=function e(t){let n=tE(t);return tw(n)?t.ownerDocument?t.ownerDocument.body:t.body:tv(n)&&tg(n)?n:e(n)}(e),r=a===(null==(i=e.ownerDocument)?void 0:i.body),o=td(a);if(r){let e=tP(o);return t.concat(o,o.visualViewport||[],tg(a)?a:[],e&&n?tS(e):[])}return t.concat(a,tS(a,[],n))}function tP(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function tR(e){let t=tj(e),n=parseFloat(t.width)||0,i=parseFloat(t.height)||0,a=tv(e),r=a?e.offsetWidth:n,o=a?e.offsetHeight:i,l=eJ(n)!==r||eJ(i)!==o;return l&&(n=r,i=o),{width:n,height:i,$:l}}function tC(e){return tf(e)?e:e.contextElement}function tO(e){let t=tC(e);if(!tv(t))return e0(1);let n=t.getBoundingClientRect(),{width:i,height:a,$:r}=tR(t),o=(r?eJ(n.width):n.width)/i,l=(r?eJ(n.height):n.height)/a;return o&&Number.isFinite(o)||(o=1),l&&Number.isFinite(l)||(l=1),{x:o,y:l}}let t_=e0(0);function tz(e){let t=td(e);return ty()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:t_}function tA(e,t,n,i){var a;void 0===t&&(t=!1),void 0===n&&(n=!1);let r=e.getBoundingClientRect(),o=tC(e),l=e0(1);t&&(i?tf(i)&&(l=tO(i)):l=tO(e));let c=(void 0===(a=n)&&(a=!1),i&&(!a||i===td(o))&&a)?tz(o):e0(0),s=(r.left+c.x)/l.x,p=(r.top+c.y)/l.y,d=r.width/l.x,u=r.height/l.y;if(o){let e=td(o),t=i&&tf(i)?td(i):i,n=e,a=tP(n);for(;a&&i&&t!==n;){let e=tO(a),t=a.getBoundingClientRect(),i=tj(a),r=t.left+(a.clientLeft+parseFloat(i.paddingLeft))*e.x,o=t.top+(a.clientTop+parseFloat(i.paddingTop))*e.y;s*=e.x,p*=e.y,d*=e.x,u*=e.y,s+=r,p+=o,a=tP(n=td(a))}}return tn({width:d,height:u,x:s,y:p})}function tD(e,t){let n=tk(e).scrollLeft;return t?t.left+n:tA(tu(e)).left+n}function tN(e,t,n){void 0===n&&(n=!1);let i=e.getBoundingClientRect();return{x:i.left+t.scrollLeft-(n?0:tD(e,i)),y:i.top+t.scrollTop}}function tT(e,t,n){let i;if("viewport"===t)i=function(e,t){let n=td(e),i=tu(e),a=n.visualViewport,r=i.clientWidth,o=i.clientHeight,l=0,c=0;if(a){r=a.width,o=a.height;let e=ty();(!e||e&&"fixed"===t)&&(l=a.offsetLeft,c=a.offsetTop)}return{width:r,height:o,x:l,y:c}}(e,n);else if("document"===t)i=function(e){let t=tu(e),n=tk(e),i=e.ownerDocument.body,a=eZ(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),r=eZ(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight),o=-n.scrollLeft+tD(e),l=-n.scrollTop;return"rtl"===tj(i).direction&&(o+=eZ(t.clientWidth,i.clientWidth)-a),{width:a,height:r,x:o,y:l}}(tu(e));else if(tf(t))i=function(e,t){let n=tA(e,!0,"fixed"===t),i=n.top+e.clientTop,a=n.left+e.clientLeft,r=tv(e)?tO(e):e0(1),o=e.clientWidth*r.x,l=e.clientHeight*r.y;return{width:o,height:l,x:a*r.x,y:i*r.y}}(t,n);else{let n=tz(e);i={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return tn(i)}function tM(e){return"static"===tj(e).position}function tL(e,t){if(!tv(e)||"fixed"===tj(e).position)return null;if(t)return t(e);let n=e.offsetParent;return tu(e)===n&&(n=n.ownerDocument.body),n}function tF(e,t){let n=td(e);if(th(e))return n;if(!tv(e)){let t=tE(e);for(;t&&!tw(t);){if(tf(t)&&!tM(t))return t;t=tE(t)}return n}let i=tL(e,t);for(;i&&["table","td","th"].includes(tp(i))&&tM(i);)i=tL(i,t);return i&&tw(i)&&tM(i)&&!tb(i)?n:i||function(e){let t=tE(e);for(;tv(t)&&!tw(t);){if(tb(t))return t;if(th(t))break;t=tE(t)}return null}(e)||n}let tI=async function(e){let t=this.getOffsetParent||tF,n=this.getDimensions,i=await n(e.floating);return{reference:function(e,t,n){let i=tv(t),a=tu(t),r="fixed"===n,o=tA(e,!0,r,t),l={scrollLeft:0,scrollTop:0},c=e0(0);if(i||!i&&!r)if(("body"!==tp(t)||tg(a))&&(l=tk(t)),i){let e=tA(t,!0,r,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else a&&(c.x=tD(a));r&&!i&&a&&(c.x=tD(a));let s=!a||i||r?e0(0):tN(a,l);return{x:o.left+l.scrollLeft-c.x-s.x,y:o.top+l.scrollTop-c.y-s.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},tq={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:i,strategy:a}=e,r="fixed"===a,o=tu(i),l=!!t&&th(t.floating);if(i===o||l&&r)return n;let c={scrollLeft:0,scrollTop:0},s=e0(1),p=e0(0),d=tv(i);if((d||!d&&!r)&&(("body"!==tp(i)||tg(o))&&(c=tk(i)),tv(i))){let e=tA(i);s=tO(i),p.x=e.x+i.clientLeft,p.y=e.y+i.clientTop}let u=!o||d||r?e0(0):tN(o,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+p.x+u.x,y:n.y*s.y-c.scrollTop*s.y+p.y+u.y}},getDocumentElement:tu,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:a}=e,r=[..."clippingAncestors"===n?th(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let i=tS(e,[],!1).filter(e=>tf(e)&&"body"!==tp(e)),a=null,r="fixed"===tj(e).position,o=r?tE(e):e;for(;tf(o)&&!tw(o);){let t=tj(o),n=tb(o);n||"fixed"!==t.position||(a=null),(r?!n&&!a:!n&&"static"===t.position&&!!a&&["absolute","fixed"].includes(a.position)||tg(o)&&!n&&function e(t,n){let i=tE(t);return!(i===n||!tf(i)||tw(i))&&("fixed"===tj(i).position||e(i,n))}(e,o))?i=i.filter(e=>e!==o):a=t,o=tE(o)}return t.set(e,i),i}(t,this._c):[].concat(n),i],o=r[0],l=r.reduce((e,n)=>{let i=tT(t,n,a);return e.top=eZ(i.top,e.top),e.right=eY(i.right,e.right),e.bottom=eY(i.bottom,e.bottom),e.left=eZ(i.left,e.left),e},tT(t,o,a));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:tF,getElementRects:tI,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=tR(e);return{width:t,height:n}},getScale:tO,isElement:tf,isRTL:function(e){return"rtl"===tj(e).direction}};function tB(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let tH=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:a,rects:r,platform:o,elements:l,middlewareData:c}=t,{element:s,padding:p=0}=e3(e,t)||{};if(null==s)return{};let d=tt(p),u={x:n,y:i},m=e5(e9(a)),f=e8(m),v=await o.getDimensions(s),x="y"===m,g=x?"clientHeight":"clientWidth",h=r.reference[f]+r.reference[m]-u[m]-r.floating[f],b=u[m]-r.reference[m],y=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s)),w=y?y[g]:0;w&&await (null==o.isElement?void 0:o.isElement(y))||(w=l.floating[g]||r.floating[f]);let j=w/2-v[f]/2-1,k=eY(d[x?"top":"left"],j),E=eY(d[x?"bottom":"right"],j),S=w-v[f]-E,P=w/2-v[f]/2+(h/2-b/2),R=eZ(k,eY(P,S)),C=!c.arrow&&null!=e6(a)&&P!==R&&r.reference[f]/2-(P<k?k:E)-v[f]/2<0,O=C?P<k?P-k:P-S:0;return{[m]:u[m]+O,data:{[m]:R,centerOffset:P-R-O,...C&&{alignmentOffset:O}},reset:C}}}),tW=(e,t,n)=>{let i=new Map,a={platform:tq,...n},r={...a.platform,_c:i};return ta(e,t,{...a,platform:r})};var tU="undefined"!=typeof document?l.useLayoutEffect:function(){};function t$(e,t){let n,i,a;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(i=n;0!=i--;)if(!t$(e[i],t[i]))return!1;return!0}if((n=(a=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(i=n;0!=i--;)if(!({}).hasOwnProperty.call(t,a[i]))return!1;for(i=n;0!=i--;){let n=a[i];if(("_owner"!==n||!e.$$typeof)&&!t$(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function tG(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tV(e,t){let n=tG(e);return Math.round(t*n)/n}function tX(e){let t=l.useRef(e);return tU(()=>{t.current=e}),t}let tK=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?tH({element:n.current,padding:i}).fn(t):{}:n?tH({element:n,padding:i}).fn(t):{}}}),tY=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,i;let{x:a,y:r,placement:o,middlewareData:l}=t,c=await tc(t,e);return o===(null==(n=l.offset)?void 0:n.placement)&&null!=(i=l.arrow)&&i.alignmentOffset?{}:{x:a+c.x,y:r+c.y,data:{...c,placement:o}}}}}(e),options:[e,t]}),tZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:a}=t,{mainAxis:r=!0,crossAxis:o=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=e3(e,t),s={x:n,y:i},p=await tr(t,c),d=e9(e4(a)),u=e5(d),m=s[u],f=s[d];if(r){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",n=m+p[e],i=m-p[t];m=eZ(n,eY(m,i))}if(o){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=f+p[e],i=f-p[t];f=eZ(n,eY(f,i))}let v=l.fn({...t,[u]:m,[d]:f});return{...v,data:{x:v.x-n,y:v.y-i,enabled:{[u]:r,[d]:o}}}}}}(e),options:[e,t]}),tJ=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:i,placement:a,rects:r,middlewareData:o}=t,{offset:l=0,mainAxis:c=!0,crossAxis:s=!0}=e3(e,t),p={x:n,y:i},d=e9(a),u=e5(d),m=p[u],f=p[d],v=e3(l,t),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(c){let e="y"===u?"height":"width",t=r.reference[u]-r.floating[e]+x.mainAxis,n=r.reference[u]+r.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(s){var g,h;let e="y"===u?"width":"height",t=["top","left"].includes(e4(a)),n=r.reference[d]-r.floating[e]+(t&&(null==(g=o.offset)?void 0:g[d])||0)+(t?0:x.crossAxis),i=r.reference[d]+r.reference[e]+(t?0:(null==(h=o.offset)?void 0:h[d])||0)-(t?x.crossAxis:0);f<n?f=n:f>i&&(f=i)}return{[u]:m,[d]:f}}}}(e),options:[e,t]}),tQ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,i,a,r,o;let{placement:l,middlewareData:c,rects:s,initialPlacement:p,platform:d,elements:u}=t,{mainAxis:m=!0,crossAxis:f=!0,fallbackPlacements:v,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:h=!0,...b}=e3(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let y=e4(l),w=e9(p),j=e4(p)===p,k=await (null==d.isRTL?void 0:d.isRTL(u.floating)),E=v||(j||!h?[te(p)]:function(e){let t=te(e);return[e7(e),t,e7(t)]}(p)),S="none"!==g;!v&&S&&E.push(...function(e,t,n,i){let a=e6(e),r=function(e,t,n){let i=["left","right"],a=["right","left"];switch(e){case"top":case"bottom":if(n)return t?a:i;return t?i:a;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(e4(e),"start"===n,i);return a&&(r=r.map(e=>e+"-"+a),t&&(r=r.concat(r.map(e7)))),r}(p,h,g,k));let P=[p,...E],R=await tr(t,b),C=[],O=(null==(i=c.flip)?void 0:i.overflows)||[];if(m&&C.push(R[y]),f){let e=function(e,t,n){void 0===n&&(n=!1);let i=e6(e),a=e5(e9(e)),r=e8(a),o="x"===a?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return t.reference[r]>t.floating[r]&&(o=te(o)),[o,te(o)]}(l,s,k);C.push(R[e[0]],R[e[1]])}if(O=[...O,{placement:l,overflows:C}],!C.every(e=>e<=0)){let e=((null==(a=c.flip)?void 0:a.index)||0)+1,t=P[e];if(t&&("alignment"!==f||w===e9(t)||O.every(e=>e.overflows[0]>0&&e9(e.placement)===w)))return{data:{index:e,overflows:O},reset:{placement:t}};let n=null==(r=O.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:r.placement;if(!n)switch(x){case"bestFit":{let e=null==(o=O.filter(e=>{if(S){let t=e9(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(n=e);break}case"initialPlacement":n=p}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),t0=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,i;let a,r,{placement:o,rects:l,platform:c,elements:s}=t,{apply:p=()=>{},...d}=e3(e,t),u=await tr(t,d),m=e4(o),f=e6(o),v="y"===e9(o),{width:x,height:g}=l.floating;"top"===m||"bottom"===m?(a=m,r=f===(await (null==c.isRTL?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(r=m,a="end"===f?"top":"bottom");let h=g-u.top-u.bottom,b=x-u.left-u.right,y=eY(g-u[a],h),w=eY(x-u[r],b),j=!t.middlewareData.shift,k=y,E=w;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(E=b),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(k=h),j&&!f){let e=eZ(u.left,0),t=eZ(u.right,0),n=eZ(u.top,0),i=eZ(u.bottom,0);v?E=x-2*(0!==e||0!==t?e+t:eZ(u.left,u.right)):k=g-2*(0!==n||0!==i?n+i:eZ(u.top,u.bottom))}await p({...t,availableWidth:E,availableHeight:k});let S=await c.getDimensions(s.floating);return x!==S.width||g!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),t1=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:i="referenceHidden",...a}=e3(e,t);switch(i){case"referenceHidden":{let e=to(await tr(t,{...a,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:tl(e)}}}case"escaped":{let e=to(await tr(t,{...a,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:tl(e)}}}default:return{}}}}}(e),options:[e,t]}),t2=(e,t)=>({...tK(e),options:[e,t]});var t3=l.forwardRef((e,t)=>{let{children:n,width:i=10,height:a=5,...r}=e;return(0,o.jsx)(e_.sG.svg,{...r,ref:t,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,o.jsx)("polygon",{points:"0,0 30,0 15,10"})})});t3.displayName="Arrow";var t4=n(66156),t6="Popper",[t5,t8]=(0,eC.A)(t6),[t9,t7]=t5(t6),ne=e=>{let{__scopePopper:t,children:n}=e,[i,a]=l.useState(null);return(0,o.jsx)(t9,{scope:t,anchor:i,onAnchorChange:a,children:n})};ne.displayName=t6;var nt="PopperAnchor",nn=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...a}=e,r=t7(nt,n),c=l.useRef(null),s=(0,eR.s)(t,c);return l.useEffect(()=>{r.onAnchorChange(i?.current||c.current)}),i?null:(0,o.jsx)(e_.sG.div,{...a,ref:s})});nn.displayName=nt;var ni="PopperContent",[na,nr]=t5(ni),no=l.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:r="center",alignOffset:c=0,arrowPadding:s=0,avoidCollisions:p=!0,collisionBoundary:d=[],collisionPadding:u=0,sticky:m="partial",hideWhenDetached:f=!1,updatePositionStrategy:v="optimized",onPlaced:x,...g}=e,h=t7(ni,n),[b,y]=l.useState(null),w=(0,eR.s)(t,e=>y(e)),[j,k]=l.useState(null),E=function(e){let[t,n]=l.useState(void 0);return(0,t4.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let i,a;if(!Array.isArray(t)||!t.length)return;let r=t[0];if("borderBoxSize"in r){let e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),S=E?.width??0,P=E?.height??0,R="number"==typeof u?u:{top:0,right:0,bottom:0,left:0,...u},C=Array.isArray(d)?d:[d],O=C.length>0,_={padding:R,boundary:C.filter(np),altBoundary:O},{refs:z,floatingStyles:A,placement:D,isPositioned:N,middlewareData:T}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:a,elements:{reference:r,floating:o}={},transform:c=!0,whileElementsMounted:s,open:p}=e,[d,u]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,f]=l.useState(i);t$(m,i)||f(i);let[v,x]=l.useState(null),[g,h]=l.useState(null),b=l.useCallback(e=>{e!==k.current&&(k.current=e,x(e))},[]),y=l.useCallback(e=>{e!==E.current&&(E.current=e,h(e))},[]),w=r||v,j=o||g,k=l.useRef(null),E=l.useRef(null),S=l.useRef(d),P=null!=s,R=tX(s),C=tX(a),O=tX(p),_=l.useCallback(()=>{if(!k.current||!E.current)return;let e={placement:t,strategy:n,middleware:m};C.current&&(e.platform=C.current),tW(k.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};z.current&&!t$(S.current,t)&&(S.current=t,ek.flushSync(()=>{u(t)}))})},[m,t,n,C,O]);tU(()=>{!1===p&&S.current.isPositioned&&(S.current.isPositioned=!1,u(e=>({...e,isPositioned:!1})))},[p]);let z=l.useRef(!1);tU(()=>(z.current=!0,()=>{z.current=!1}),[]),tU(()=>{if(w&&(k.current=w),j&&(E.current=j),w&&j){if(R.current)return R.current(w,j,_);_()}},[w,j,_,R,P]);let A=l.useMemo(()=>({reference:k,floating:E,setReference:b,setFloating:y}),[b,y]),D=l.useMemo(()=>({reference:w,floating:j}),[w,j]),N=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=tV(D.floating,d.x),i=tV(D.floating,d.y);return c?{...e,transform:"translate("+t+"px, "+i+"px)",...tG(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:i}},[n,c,D.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:_,refs:A,elements:D,floatingStyles:N}),[d,_,A,D,N])}({strategy:"fixed",placement:i+("center"!==r?"-"+r:""),whileElementsMounted:(...e)=>(function(e,t,n,i){let a;void 0===i&&(i={});let{ancestorScroll:r=!0,ancestorResize:o=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:s=!1}=i,p=tC(e),d=r||o?[...p?tS(p):[],...tS(t)]:[];d.forEach(e=>{r&&e.addEventListener("scroll",n,{passive:!0}),o&&e.addEventListener("resize",n)});let u=p&&c?function(e,t){let n,i=null,a=tu(e);function r(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function o(l,c){void 0===l&&(l=!1),void 0===c&&(c=1),r();let s=e.getBoundingClientRect(),{left:p,top:d,width:u,height:m}=s;if(l||t(),!u||!m)return;let f=eQ(d),v=eQ(a.clientWidth-(p+u)),x={rootMargin:-f+"px "+-v+"px "+-eQ(a.clientHeight-(d+m))+"px "+-eQ(p)+"px",threshold:eZ(0,eY(1,c))||1},g=!0;function h(t){let i=t[0].intersectionRatio;if(i!==c){if(!g)return o();i?o(!1,i):n=setTimeout(()=>{o(!1,1e-7)},1e3)}1!==i||tB(s,e.getBoundingClientRect())||o(),g=!1}try{i=new IntersectionObserver(h,{...x,root:a.ownerDocument})}catch(e){i=new IntersectionObserver(h,x)}i.observe(e)}(!0),r}(p,n):null,m=-1,f=null;l&&(f=new ResizeObserver(e=>{let[i]=e;i&&i.target===p&&f&&(f.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=f)||e.observe(t)})),n()}),p&&!s&&f.observe(p),f.observe(t));let v=s?tA(e):null;return s&&function t(){let i=tA(e);v&&!tB(v,i)&&n(),v=i,a=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{r&&e.removeEventListener("scroll",n),o&&e.removeEventListener("resize",n)}),null==u||u(),null==(e=f)||e.disconnect(),f=null,s&&cancelAnimationFrame(a)}})(...e,{animationFrame:"always"===v}),elements:{reference:h.anchor},middleware:[tY({mainAxis:a+P,alignmentAxis:c}),p&&tZ({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?tJ():void 0,..._}),p&&tQ({..._}),t0({..._,apply:({elements:e,rects:t,availableWidth:n,availableHeight:i})=>{let{width:a,height:r}=t.reference,o=e.floating.style;o.setProperty("--radix-popper-available-width",`${n}px`),o.setProperty("--radix-popper-available-height",`${i}px`),o.setProperty("--radix-popper-anchor-width",`${a}px`),o.setProperty("--radix-popper-anchor-height",`${r}px`)}}),j&&t2({element:j,padding:s}),nd({arrowWidth:S,arrowHeight:P}),f&&t1({strategy:"referenceHidden",..._})]}),[M,L]=nu(D),F=(0,ez.c)(x);(0,t4.N)(()=>{N&&F?.()},[N,F]);let I=T.arrow?.x,q=T.arrow?.y,B=T.arrow?.centerOffset!==0,[H,W]=l.useState();return(0,t4.N)(()=>{b&&W(window.getComputedStyle(b).zIndex)},[b]),(0,o.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...A,transform:N?A.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:H,"--radix-popper-transform-origin":[T.transformOrigin?.x,T.transformOrigin?.y].join(" "),...T.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,o.jsx)(na,{scope:n,placedSide:M,onArrowChange:k,arrowX:I,arrowY:q,shouldHideArrow:B,children:(0,o.jsx)(e_.sG.div,{"data-side":M,"data-align":L,...g,ref:w,style:{...g.style,animation:N?void 0:"none"}})})})});no.displayName=ni;var nl="PopperArrow",nc={top:"bottom",right:"left",bottom:"top",left:"right"},ns=l.forwardRef(function(e,t){let{__scopePopper:n,...i}=e,a=nr(nl,n),r=nc[a.placedSide];return(0,o.jsx)("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[r]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:(0,o.jsx)(t3,{...i,ref:t,style:{...i.style,display:"block"}})})});function np(e){return null!==e}ns.displayName=nl;var nd=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:i,middlewareData:a}=t,r=a.arrow?.centerOffset!==0,o=r?0:e.arrowWidth,l=r?0:e.arrowHeight,[c,s]=nu(n),p={start:"0%",center:"50%",end:"100%"}[s],d=(a.arrow?.x??0)+o/2,u=(a.arrow?.y??0)+l/2,m="",f="";return"bottom"===c?(m=r?p:`${d}px`,f=`${-l}px`):"top"===c?(m=r?p:`${d}px`,f=`${i.floating.height+l}px`):"right"===c?(m=`${-l}px`,f=r?p:`${u}px`):"left"===c&&(m=`${i.floating.width+l}px`,f=r?p:`${u}px`),{data:{x:m,y:f}}}});function nu(e){let[t,n="center"]=e.split("-");return[t,n]}var nm=l.forwardRef((e,t)=>{let{container:n,...i}=e,[a,r]=l.useState(!1);(0,t4.N)(()=>r(!0),[]);let c=n||a&&globalThis?.document?.body;return c?ek.createPortal((0,o.jsx)(e_.sG.div,{...i,ref:t}),c):null});nm.displayName="Portal";var nf=n(65551),nv=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,o.jsx)(e_.sG.span,{...e,ref:t,style:{...nv,...e.style}})).displayName="VisuallyHidden";var nx=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},ng=new WeakMap,nh=new WeakMap,nb={},ny=0,nw=function(e){return e&&(e.host||nw(e.parentNode))},nj=function(e,t,n,i){var a=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=nw(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});nb[n]||(nb[n]=new WeakMap);var r=nb[n],o=[],l=new Set,c=new Set(a),s=function(e){!e||l.has(e)||(l.add(e),s(e.parentNode))};a.forEach(s);var p=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))p(e);else try{var t=e.getAttribute(i),a=null!==t&&"false"!==t,c=(ng.get(e)||0)+1,s=(r.get(e)||0)+1;ng.set(e,c),r.set(e,s),o.push(e),1===c&&a&&nh.set(e,!0),1===s&&e.setAttribute(n,"true"),a||e.setAttribute(i,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return p(t),l.clear(),ny++,function(){o.forEach(function(e){var t=ng.get(e)-1,a=r.get(e)-1;ng.set(e,t),r.set(e,a),t||(nh.has(e)||e.removeAttribute(i),nh.delete(e)),a||e.removeAttribute(n)}),--ny||(ng=new WeakMap,ng=new WeakMap,nh=new WeakMap,nb={})}},nk=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),a=t||nx(e);return a?(i.push.apply(i,Array.from(a.querySelectorAll("[aria-live], script"))),nj(i,a,n,"aria-hidden")):function(){return null}},nE="right-scroll-bar-position",nS="width-before-scroll-bar";function nP(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var nR="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,nC=new WeakMap;function nO(e){return e}var n_=function(e){void 0===e&&(e={});var t,n,i,a,r=(t=null,void 0===n&&(n=nO),i=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=n(e,a);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){a=!0;var t=[];if(i.length){var n=i;i=[],n.forEach(e),t=i}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),i={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),i}}}});return r.options=s({async:!0,ssr:!1},e),r}(),nz=function(){},nA=l.forwardRef(function(e,t){var n,i,a,r,o=l.useRef(null),c=l.useState({onScrollCapture:nz,onWheelCapture:nz,onTouchMoveCapture:nz}),d=c[0],u=c[1],m=e.forwardProps,f=e.children,v=e.className,x=e.removeScrollBar,g=e.enabled,h=e.shards,b=e.sideCar,y=e.noRelative,w=e.noIsolation,j=e.inert,k=e.allowPinchZoom,E=e.as,S=e.gapMode,P=p(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[o,t],i=function(e){return n.forEach(function(t){return nP(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:i,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=i,r=a.facade,nR(function(){var e=nC.get(r);if(e){var t=new Set(e),i=new Set(n),a=r.current;t.forEach(function(e){i.has(e)||nP(e,null)}),i.forEach(function(e){t.has(e)||nP(e,a)})}nC.set(r,n)},[n]),r),C=s(s({},P),d);return l.createElement(l.Fragment,null,g&&l.createElement(b,{sideCar:n_,removeScrollBar:x,shards:h,noRelative:y,noIsolation:w,inert:j,setCallbacks:u,allowPinchZoom:!!k,lockRef:o,gapMode:S}),m?l.cloneElement(l.Children.only(f),s(s({},C),{ref:R})):l.createElement(void 0===E?"div":E,s({},C,{className:v,ref:R}),f))});nA.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},nA.classNames={fullWidth:nS,zeroRight:nE};var nD=function(e){var t=e.sideCar,n=p(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw Error("Sidecar medium not found");return l.createElement(i,s({},n))};nD.isSideCarExport=!0;var nN=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,o;(a=t).styleSheet?a.styleSheet.cssText=i:a.appendChild(document.createTextNode(i)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},nT=function(){var e=nN();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},nM=function(){var e=nT();return function(t){return e(t.styles,t.dynamic),null}},nL={left:0,top:0,right:0,gap:0},nF=function(e){return parseInt(e||"",10)||0},nI=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],i=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[nF(n),nF(i),nF(a)]},nq=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return nL;var t=nI(e),n=document.documentElement.clientWidth,i=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,i-n+t[2]-t[0])}},nB=nM(),nH="data-scroll-locked",nW=function(e,t,n,i){var a=e.left,r=e.top,o=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(i,";\n   padding-right: ").concat(l,"px ").concat(i,";\n  }\n  body[").concat(nH,"] {\n    overflow: hidden ").concat(i,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(i,";"),"margin"===n&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(r,"px;\n    padding-right: ").concat(o,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(i,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(i,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(nE," {\n    right: ").concat(l,"px ").concat(i,";\n  }\n  \n  .").concat(nS," {\n    margin-right: ").concat(l,"px ").concat(i,";\n  }\n  \n  .").concat(nE," .").concat(nE," {\n    right: 0 ").concat(i,";\n  }\n  \n  .").concat(nS," .").concat(nS," {\n    margin-right: 0 ").concat(i,";\n  }\n  \n  body[").concat(nH,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},nU=function(){var e=parseInt(document.body.getAttribute(nH)||"0",10);return isFinite(e)?e:0},n$=function(){l.useEffect(function(){return document.body.setAttribute(nH,(nU()+1).toString()),function(){var e=nU()-1;e<=0?document.body.removeAttribute(nH):document.body.setAttribute(nH,e.toString())}},[])},nG=function(e){var t=e.noRelative,n=e.noImportant,i=e.gapMode,a=void 0===i?"margin":i;n$();var r=l.useMemo(function(){return nq(a)},[a]);return l.createElement(nB,{styles:nW(r,!t,a,n?"":"!important")})},nV=!1;if("undefined"!=typeof window)try{var nX=Object.defineProperty({},"passive",{get:function(){return nV=!0,!0}});window.addEventListener("test",nX,nX),window.removeEventListener("test",nX,nX)}catch(e){nV=!1}var nK=!!nV&&{passive:!1},nY=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nZ=function(e,t){var n=t.ownerDocument,i=t;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),nJ(e,i)){var a=nQ(e,i);if(a[1]>a[2])return!0}i=i.parentNode}while(i&&i!==n.body);return!1},nJ=function(e,t){return"v"===e?nY(t,"overflowY"):nY(t,"overflowX")},nQ=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},n0=function(e,t,n,i,a){var r,o=(r=window.getComputedStyle(t).direction,"h"===e&&"rtl"===r?-1:1),l=o*i,c=n.target,s=t.contains(c),p=!1,d=l>0,u=0,m=0;do{if(!c)break;var f=nQ(e,c),v=f[0],x=f[1]-f[2]-o*v;(v||x)&&nJ(e,c)&&(u+=x,m+=v);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&c!==document.body||s&&(t.contains(c)||t===c));return d&&(a&&1>Math.abs(u)||!a&&l>u)?p=!0:!d&&(a&&1>Math.abs(m)||!a&&-l>m)&&(p=!0),p},n1=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},n2=function(e){return[e.deltaX,e.deltaY]},n3=function(e){return e&&"current"in e?e.current:e},n4=0,n6=[];let n5=(i=function(e){var t=l.useRef([]),n=l.useRef([0,0]),i=l.useRef(),a=l.useState(n4++)[0],r=l.useState(nM)[0],o=l.useRef(e);l.useEffect(function(){o.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,n){if(n||2==arguments.length)for(var i,a=0,r=t.length;a<r;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(n3),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!o.current.allowPinchZoom;var a,r=n1(e),l=n.current,c="deltaX"in e?e.deltaX:l[0]-r[0],s="deltaY"in e?e.deltaY:l[1]-r[1],p=e.target,d=Math.abs(c)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===p.type)return!1;var u=nZ(d,p);if(!u)return!0;if(u?a=d:(a="v"===d?"h":"v",u=nZ(d,p)),!u)return!1;if(!i.current&&"changedTouches"in e&&(c||s)&&(i.current=a),!a)return!0;var m=i.current||a;return n0(m,t,e,"h"===m?c:s,!0)},[]),s=l.useCallback(function(e){if(n6.length&&n6[n6.length-1]===r){var n="deltaY"in e?n2(e):n1(e),i=t.current.filter(function(t){var i;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(i=t.delta,i[0]===n[0]&&i[1]===n[1])})[0];if(i&&i.should){e.cancelable&&e.preventDefault();return}if(!i){var a=(o.current.shards||[]).map(n3).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?c(e,a[0]):!o.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),p=l.useCallback(function(e,n,i,a){var r={name:e,delta:n,target:i,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(i)};t.current.push(r),setTimeout(function(){t.current=t.current.filter(function(e){return e!==r})},1)},[]),d=l.useCallback(function(e){n.current=n1(e),i.current=void 0},[]),u=l.useCallback(function(t){p(t.type,n2(t),t.target,c(t,e.lockRef.current))},[]),m=l.useCallback(function(t){p(t.type,n1(t),t.target,c(t,e.lockRef.current))},[]);l.useEffect(function(){return n6.push(r),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:m}),document.addEventListener("wheel",s,nK),document.addEventListener("touchmove",s,nK),document.addEventListener("touchstart",d,nK),function(){n6=n6.filter(function(e){return e!==r}),document.removeEventListener("wheel",s,nK),document.removeEventListener("touchmove",s,nK),document.removeEventListener("touchstart",d,nK)}},[]);var f=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(r,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,f?l.createElement(nG,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},n_.useMedium(i),nD);var n8=l.forwardRef(function(e,t){return l.createElement(nA,s({},e,{ref:t,sideCar:n5}))});n8.classNames=nA.classNames;var n9=[" ","Enter","ArrowUp","ArrowDown"],n7=[" ","Enter"],ie="Select",[it,ii,ia]=(0,eP.N)(ie),[ir,io]=(0,eC.A)(ie,[ia,t8]),il=t8(),[ic,is]=ir(ie),[ip,id]=ir(ie),iu=e=>{let{__scopeSelect:t,children:n,open:i,defaultOpen:a,onOpenChange:r,value:c,defaultValue:s,onValueChange:p,dir:d,name:u,autoComplete:m,disabled:f,required:v,form:x}=e,g=il(t),[h,b]=l.useState(null),[y,w]=l.useState(null),[j,k]=l.useState(!1),E=(0,eO.jH)(d),[S,P]=(0,nf.i)({prop:i,defaultProp:a??!1,onChange:r,caller:ie}),[R,C]=(0,nf.i)({prop:c,defaultProp:s,onChange:p,caller:ie}),O=l.useRef(null),_=!h||x||!!h.closest("form"),[z,A]=l.useState(new Set),D=Array.from(z).map(e=>e.props.value).join(";");return(0,o.jsx)(ne,{...g,children:(0,o.jsxs)(ic,{required:v,scope:t,trigger:h,onTriggerChange:b,valueNode:y,onValueNodeChange:w,valueNodeHasChildren:j,onValueNodeHasChildrenChange:k,contentId:(0,eX.B)(),value:R,onValueChange:C,open:S,onOpenChange:P,dir:E,triggerPointerDownPosRef:O,disabled:f,children:[(0,o.jsx)(it.Provider,{scope:t,children:(0,o.jsx)(ip,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{A(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{A(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),_?(0,o.jsxs)(iY,{"aria-hidden":!0,required:v,tabIndex:-1,name:u,autoComplete:m,value:R,onChange:e=>C(e.target.value),disabled:f,form:x,children:[void 0===R?(0,o.jsx)("option",{value:""}):null,Array.from(z)]},D):null]})})};iu.displayName=ie;var im="SelectTrigger",iv=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:i=!1,...a}=e,r=il(n),c=is(im,n),s=c.disabled||i,p=(0,eR.s)(t,c.onTriggerChange),d=ii(n),u=l.useRef("touch"),[m,f,v]=iJ(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),i=iQ(t,e,n);void 0!==i&&c.onValueChange(i.value)}),x=e=>{s||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,o.jsx)(nn,{asChild:!0,...r,children:(0,o.jsx)(e_.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":iZ(c.value)?"":void 0,...a,ref:p,onClick:(0,eS.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==u.current&&x(e)}),onPointerDown:(0,eS.m)(a.onPointerDown,e=>{u.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,eS.m)(a.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||f(e.key),(!t||" "!==e.key)&&n9.includes(e.key)&&(x(),e.preventDefault())})})})});iv.displayName=im;var ix="SelectValue",ig=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,children:r,placeholder:l="",...c}=e,s=is(ix,n),{onValueNodeHasChildrenChange:p}=s,d=void 0!==r,u=(0,eR.s)(t,s.onValueNodeChange);return(0,t4.N)(()=>{p(d)},[p,d]),(0,o.jsx)(e_.sG.span,{...c,ref:u,style:{pointerEvents:"none"},children:iZ(s.value)?(0,o.jsx)(o.Fragment,{children:l}):r})});ig.displayName=ix;var ih=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:i,...a}=e;return(0,o.jsx)(e_.sG.span,{"aria-hidden":!0,...a,ref:t,children:i||"▼"})});ih.displayName="SelectIcon";var ib=e=>(0,o.jsx)(nm,{asChild:!0,...e});ib.displayName="SelectPortal";var iy="SelectContent",iw=l.forwardRef((e,t)=>{let n=is(iy,e.__scopeSelect),[i,a]=l.useState();return((0,t4.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,o.jsx)(iS,{...e,ref:t}):i?ek.createPortal((0,o.jsx)(ij,{scope:e.__scopeSelect,children:(0,o.jsx)(it.Slot,{scope:e.__scopeSelect,children:(0,o.jsx)("div",{children:e.children})})}),i):null});iw.displayName=iy;var[ij,ik]=ir(iy),iE=(0,ec.TL)("SelectContent.RemoveScroll"),iS=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:i="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:r,onPointerDownOutside:c,side:s,sideOffset:p,align:d,alignOffset:u,arrowPadding:m,collisionBoundary:f,collisionPadding:v,sticky:x,hideWhenDetached:g,avoidCollisions:h,...b}=e,y=is(iy,n),[w,j]=l.useState(null),[k,E]=l.useState(null),S=(0,eR.s)(t,e=>j(e)),[P,R]=l.useState(null),[C,O]=l.useState(null),_=ii(n),[z,A]=l.useState(!1),D=l.useRef(!1);l.useEffect(()=>{if(w)return nk(w)},[w]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??eF()),document.body.insertAdjacentElement("beforeend",e[1]??eF()),eL++,()=>{1===eL&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),eL--}},[]);let N=l.useCallback(e=>{let[t,...n]=_().map(e=>e.ref.current),[i]=n.slice(-1),a=document.activeElement;for(let n of e)if(n===a||(n?.scrollIntoView({block:"nearest"}),n===t&&k&&(k.scrollTop=0),n===i&&k&&(k.scrollTop=k.scrollHeight),n?.focus(),document.activeElement!==a))return},[_,k]),T=l.useCallback(()=>N([P,w]),[N,P,w]);l.useEffect(()=>{z&&T()},[z,T]);let{onOpenChange:M,triggerPointerDownPosRef:L}=y;l.useEffect(()=>{if(w){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(L.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(L.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():w.contains(n.target)||M(!1),document.removeEventListener("pointermove",t),L.current=null};return null!==L.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[w,M,L]),l.useEffect(()=>{let e=()=>M(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[M]);let[F,I]=iJ(e=>{let t=_().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),i=iQ(t,e,n);i&&setTimeout(()=>i.ref.current.focus())}),q=l.useCallback((e,t,n)=>{let i=!D.current&&!n;(void 0!==y.value&&y.value===t||i)&&(R(e),i&&(D.current=!0))},[y.value]),B=l.useCallback(()=>w?.focus(),[w]),H=l.useCallback((e,t,n)=>{let i=!D.current&&!n;(void 0!==y.value&&y.value===t||i)&&O(e)},[y.value]),W="popper"===i?iR:iP,U=W===iR?{side:s,sideOffset:p,align:d,alignOffset:u,arrowPadding:m,collisionBoundary:f,collisionPadding:v,sticky:x,hideWhenDetached:g,avoidCollisions:h}:{};return(0,o.jsx)(ij,{scope:n,content:w,viewport:k,onViewportChange:E,itemRefCallback:q,selectedItem:P,onItemLeave:B,itemTextRefCallback:H,focusSelectedItem:T,selectedItemText:C,position:i,isPositioned:z,searchRef:F,children:(0,o.jsx)(n8,{as:iE,allowPinchZoom:!0,children:(0,o.jsx)(eH,{asChild:!0,trapped:y.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,eS.m)(a,e=>{y.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,o.jsx)(eN,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:r,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:(0,o.jsx)(W,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:e=>e.preventDefault(),...b,...U,onPlaced:()=>A(!0),ref:S,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,eS.m)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||I(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=_().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,i=t.indexOf(n);t=t.slice(i+1)}setTimeout(()=>N(t)),e.preventDefault()}})})})})})})});iS.displayName="SelectContentImpl";var iP=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:i,...a}=e,r=is(iy,n),c=ik(iy,n),[s,p]=l.useState(null),[d,u]=l.useState(null),m=(0,eR.s)(t,e=>u(e)),f=ii(n),v=l.useRef(!1),x=l.useRef(!0),{viewport:g,selectedItem:h,selectedItemText:b,focusSelectedItem:y}=c,w=l.useCallback(()=>{if(r.trigger&&r.valueNode&&s&&d&&g&&h&&b){let e=r.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=r.valueNode.getBoundingClientRect(),a=b.getBoundingClientRect();if("rtl"!==r.dir){let i=a.left-t.left,r=n.left-i,o=e.left-r,l=e.width+o,c=Math.max(l,t.width),p=eE(r,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=l+"px",s.style.left=p+"px"}else{let i=t.right-a.right,r=window.innerWidth-n.right-i,o=window.innerWidth-e.right-r,l=e.width+o,c=Math.max(l,t.width),p=eE(r,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=l+"px",s.style.right=p+"px"}let o=f(),l=window.innerHeight-20,c=g.scrollHeight,p=window.getComputedStyle(d),u=parseInt(p.borderTopWidth,10),m=parseInt(p.paddingTop,10),x=parseInt(p.borderBottomWidth,10),y=u+m+c+parseInt(p.paddingBottom,10)+x,w=Math.min(5*h.offsetHeight,y),j=window.getComputedStyle(g),k=parseInt(j.paddingTop,10),E=parseInt(j.paddingBottom,10),S=e.top+e.height/2-10,P=h.offsetHeight/2,R=u+m+(h.offsetTop+P);if(R<=S){let e=o.length>0&&h===o[o.length-1].ref.current;s.style.bottom="0px";let t=Math.max(l-S,P+(e?E:0)+(d.clientHeight-g.offsetTop-g.offsetHeight)+x);s.style.height=R+t+"px"}else{let e=o.length>0&&h===o[0].ref.current;s.style.top="0px";let t=Math.max(S,u+g.offsetTop+(e?k:0)+P);s.style.height=t+(y-R)+"px",g.scrollTop=R-S+g.offsetTop}s.style.margin="10px 0",s.style.minHeight=w+"px",s.style.maxHeight=l+"px",i?.(),requestAnimationFrame(()=>v.current=!0)}},[f,r.trigger,r.valueNode,s,d,g,h,b,r.dir,i]);(0,t4.N)(()=>w(),[w]);let[j,k]=l.useState();(0,t4.N)(()=>{d&&k(window.getComputedStyle(d).zIndex)},[d]);let E=l.useCallback(e=>{e&&!0===x.current&&(w(),y?.(),x.current=!1)},[w,y]);return(0,o.jsx)(iC,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:v,onScrollButtonChange:E,children:(0,o.jsx)("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,o.jsx)(e_.sG.div,{...a,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});iP.displayName="SelectItemAlignedPosition";var iR=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:i="start",collisionPadding:a=10,...r}=e,l=il(n);return(0,o.jsx)(no,{...l,...r,ref:t,align:i,collisionPadding:a,style:{boxSizing:"border-box",...r.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});iR.displayName="SelectPopperPosition";var[iC,iO]=ir(iy,{}),i_="SelectViewport",iz=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:i,...a}=e,r=ik(i_,n),c=iO(i_,n),s=(0,eR.s)(t,r.onViewportChange),p=l.useRef(0);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,o.jsx)(it.Slot,{scope:n,children:(0,o.jsx)(e_.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,eS.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:i}=c;if(i?.current&&n){let e=Math.abs(p.current-t.scrollTop);if(e>0){let i=window.innerHeight-20,a=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(a<i){let r=a+e,o=Math.min(i,r),l=r-o;n.style.height=o+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}p.current=t.scrollTop})})})]})});iz.displayName=i_;var iA="SelectGroup",[iD,iN]=ir(iA);l.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e,a=(0,eX.B)();return(0,o.jsx)(iD,{scope:n,id:a,children:(0,o.jsx)(e_.sG.div,{role:"group","aria-labelledby":a,...i,ref:t})})}).displayName=iA;var iT="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e,a=iN(iT,n);return(0,o.jsx)(e_.sG.div,{id:a.id,...i,ref:t})}).displayName=iT;var iM="SelectItem",[iL,iF]=ir(iM),iI=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:i,disabled:a=!1,textValue:r,...c}=e,s=is(iM,n),p=ik(iM,n),d=s.value===i,[u,m]=l.useState(r??""),[f,v]=l.useState(!1),x=(0,eR.s)(t,e=>p.itemRefCallback?.(e,i,a)),g=(0,eX.B)(),h=l.useRef("touch"),b=()=>{a||(s.onValueChange(i),s.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,o.jsx)(iL,{scope:n,value:i,disabled:a,textId:g,isSelected:d,onItemTextChange:l.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,o.jsx)(it.ItemSlot,{scope:n,value:i,disabled:a,textValue:u,children:(0,o.jsx)(e_.sG.div,{role:"option","aria-labelledby":g,"data-highlighted":f?"":void 0,"aria-selected":d&&f,"data-state":d?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...c,ref:x,onFocus:(0,eS.m)(c.onFocus,()=>v(!0)),onBlur:(0,eS.m)(c.onBlur,()=>v(!1)),onClick:(0,eS.m)(c.onClick,()=>{"mouse"!==h.current&&b()}),onPointerUp:(0,eS.m)(c.onPointerUp,()=>{"mouse"===h.current&&b()}),onPointerDown:(0,eS.m)(c.onPointerDown,e=>{h.current=e.pointerType}),onPointerMove:(0,eS.m)(c.onPointerMove,e=>{h.current=e.pointerType,a?p.onItemLeave?.():"mouse"===h.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,eS.m)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&p.onItemLeave?.()}),onKeyDown:(0,eS.m)(c.onKeyDown,e=>{(p.searchRef?.current===""||" "!==e.key)&&(n7.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});iI.displayName=iM;var iq="SelectItemText",iB=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...r}=e,c=is(iq,n),s=ik(iq,n),p=iF(iq,n),d=id(iq,n),[u,m]=l.useState(null),f=(0,eR.s)(t,e=>m(e),p.onItemTextChange,e=>s.itemTextRefCallback?.(e,p.value,p.disabled)),v=u?.textContent,x=l.useMemo(()=>(0,o.jsx)("option",{value:p.value,disabled:p.disabled,children:v},p.value),[p.disabled,p.value,v]),{onNativeOptionAdd:g,onNativeOptionRemove:h}=d;return(0,t4.N)(()=>(g(x),()=>h(x)),[g,h,x]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(e_.sG.span,{id:p.textId,...r,ref:f}),p.isSelected&&c.valueNode&&!c.valueNodeHasChildren?ek.createPortal(r.children,c.valueNode):null]})});iB.displayName=iq;var iH="SelectItemIndicator",iW=l.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e;return iF(iH,n).isSelected?(0,o.jsx)(e_.sG.span,{"aria-hidden":!0,...i,ref:t}):null});iW.displayName=iH;var iU="SelectScrollUpButton",i$=l.forwardRef((e,t)=>{let n=ik(iU,e.__scopeSelect),i=iO(iU,e.__scopeSelect),[a,r]=l.useState(!1),c=(0,eR.s)(t,i.onScrollButtonChange);return(0,t4.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){r(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),a?(0,o.jsx)(iX,{...e,ref:c,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});i$.displayName=iU;var iG="SelectScrollDownButton",iV=l.forwardRef((e,t)=>{let n=ik(iG,e.__scopeSelect),i=iO(iG,e.__scopeSelect),[a,r]=l.useState(!1),c=(0,eR.s)(t,i.onScrollButtonChange);return(0,t4.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;r(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),a?(0,o.jsx)(iX,{...e,ref:c,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});iV.displayName=iG;var iX=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:i,...a}=e,r=ik("SelectScrollButton",n),c=l.useRef(null),s=ii(n),p=l.useCallback(()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)},[]);return l.useEffect(()=>()=>p(),[p]),(0,t4.N)(()=>{let e=s().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[s]),(0,o.jsx)(e_.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,eS.m)(a.onPointerDown,()=>{null===c.current&&(c.current=window.setInterval(i,50))}),onPointerMove:(0,eS.m)(a.onPointerMove,()=>{r.onItemLeave?.(),null===c.current&&(c.current=window.setInterval(i,50))}),onPointerLeave:(0,eS.m)(a.onPointerLeave,()=>{p()})})});l.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e;return(0,o.jsx)(e_.sG.div,{"aria-hidden":!0,...i,ref:t})}).displayName="SelectSeparator";var iK="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e,a=il(n),r=is(iK,n),l=ik(iK,n);return r.open&&"popper"===l.position?(0,o.jsx)(ns,{...a,...i,ref:t}):null}).displayName=iK;var iY=l.forwardRef(({__scopeSelect:e,value:t,...n},i)=>{let a=l.useRef(null),r=(0,eR.s)(i,a),c=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return l.useEffect(()=>{let e=a.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==t&&n){let i=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(i)}},[c,t]),(0,o.jsx)(e_.sG.select,{...n,style:{...nv,...n.style},ref:r,defaultValue:t})});function iZ(e){return""===e||void 0===e}function iJ(e){let t=(0,ez.c)(e),n=l.useRef(""),i=l.useRef(0),a=l.useCallback(e=>{let a=n.current+e;t(a),function e(t){n.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(a)},[t]),r=l.useCallback(()=>{n.current="",window.clearTimeout(i.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(i.current),[]),[n,a,r]}function iQ(e,t,n){var i,a;let r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1,l=(i=e,a=Math.max(o,0),i.map((e,t)=>i[(a+t)%i.length]));1===r.length&&(l=l.filter(e=>e!==n));let c=l.find(e=>e.textValue.toLowerCase().startsWith(r.toLowerCase()));return c!==n?c:void 0}iY.displayName="SelectBubbleInput";let i0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i1=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),i2=e=>{let t=i1(e);return t.charAt(0).toUpperCase()+t.slice(1)},i3=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),i4=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i6={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i5=(0,l.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:a="",children:r,iconNode:o,...c},s)=>(0,l.createElement)("svg",{ref:s,...i6,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:i3("lucide",a),...!r&&!i4(c)&&{"aria-hidden":"true"},...c},[...o.map(([e,t])=>(0,l.createElement)(e,t)),...Array.isArray(r)?r:[r]])),i8=(e,t)=>{let n=(0,l.forwardRef)(({className:n,...i},a)=>(0,l.createElement)(i5,{ref:a,iconNode:t,className:i3(`lucide-${i0(i2(e))}`,`lucide-${e}`,n),...i}));return n.displayName=i2(e),n},i9=i8("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),i7=i8("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),ae=i8("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);function at({...e}){return(0,o.jsx)(iu,{"data-slot":"select",...e})}function an({...e}){return(0,o.jsx)(ig,{"data-slot":"select-value",...e})}function ai({className:e,size:t="default",children:n,...i}){return(0,o.jsxs)(iv,{"data-slot":"select-trigger","data-size":t,className:(0,em.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[n,(0,o.jsx)(ih,{asChild:!0,children:(0,o.jsx)(i9,{className:"size-4 opacity-50"})})]})}function aa({className:e,children:t,position:n="popper",...i}){return(0,o.jsx)(ib,{children:(0,o.jsxs)(iw,{"data-slot":"select-content",className:(0,em.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...i,children:[(0,o.jsx)(ao,{}),(0,o.jsx)(iz,{className:(0,em.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,o.jsx)(al,{})]})})}function ar({className:e,children:t,...n}){return(0,o.jsxs)(iI,{"data-slot":"select-item",className:(0,em.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[(0,o.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,o.jsx)(iW,{children:(0,o.jsx)(i7,{className:"size-4"})})}),(0,o.jsx)(iB,{children:t})]})}function ao({className:e,...t}){return(0,o.jsx)(i$,{"data-slot":"select-scroll-up-button",className:(0,em.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,o.jsx)(ae,{className:"size-4"})})}function al({className:e,...t}){return(0,o.jsx)(iV,{"data-slot":"select-scroll-down-button",className:(0,em.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,o.jsx)(i9,{className:"size-4"})})}let ac=eu("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function as({className:e,variant:t,...n}){return(0,o.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,em.cn)(ac({variant:t}),e),...n})}function ap({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"alert-description",className:(0,em.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}let ad=i8("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),au=i8("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),am=i8("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),af=i8("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),av=i8("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var ax=n(31261),ag=n.n(ax);function ah({userId:e}){let[t,n]=(0,l.useState)([]),[i,a]=(0,l.useState)(!1),[r,c]=(0,l.useState)([]),{getRootProps:s,getInputProps:p,isDragActive:d}=er({onDrop:(0,l.useCallback)(e=>{let t=e.map((e,t)=>({id:`temp-${Date.now()}-${t}`,imageFile:e,imageUrl:URL.createObjectURL(e),name:"",brand:"",size:"",price:"",status:"draft"}));n(e=>[...e,...t])},[]),accept:{"image/*":[".jpeg",".jpg",".png",".webp"]},multiple:!0}),u=(e,t,i)=>{n(n=>n.map((n,a)=>a===e?{...n,[t]:i}:n))},m=e=>{n(t=>{let n=[...t];return URL.revokeObjectURL(n[e].imageUrl),n.splice(e,1),n})},f=e=>{n(t=>t.map(t=>({...t,brand:e})))},v=e=>{n(t=>t.map(t=>({...t,price:e})))},x=()=>{let e=[];return t.forEach((t,n)=>{t.name.trim()||e.push(`Item ${n+1}: Name is required`),(!t.price||0>=parseFloat(t.price))&&e.push(`Item ${n+1}: Valid price is required`)}),c(e),0===e.length},g=async()=>{if(x()){a(!0);try{console.log("Saving items:",t),await new Promise(e=>setTimeout(e,2e3)),n([]),c([])}catch(e){console.error("Error saving items:",e),c(["Failed to save items. Please try again."])}finally{a(!1)}}};return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)(eh,{children:[(0,o.jsxs)(eb,{children:[(0,o.jsx)(ey,{children:"Upload Images"}),(0,o.jsx)(ew,{children:"Drag and drop multiple images or click to select files"})]}),(0,o.jsxs)(ej,{children:[(0,o.jsxs)("div",{...s(),className:`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${d?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"}`,children:[(0,o.jsx)("input",{...p()}),(0,o.jsx)(ad,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),d?(0,o.jsx)("p",{className:"text-blue-600",children:"Drop the images here..."}):(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-gray-600 mb-2",children:"Drag & drop images here, or click to select"}),(0,o.jsx)("p",{className:"text-sm text-gray-500",children:"Supports JPEG, PNG, WebP files"})]})]}),t.length>0&&(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsxs)("p",{className:"text-sm text-gray-600",children:[t.length," image",1!==t.length?"s":""," uploaded"]})})]})]}),t.length>0&&(0,o.jsxs)(eh,{children:[(0,o.jsxs)(eb,{children:[(0,o.jsx)(ey,{children:"Bulk Actions"}),(0,o.jsx)(ew,{children:"Apply values to all items at once"})]}),(0,o.jsx)(ej,{children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)(eg.Label,{htmlFor:"bulk-brand",children:"Set Brand for All"}),(0,o.jsx)("div",{className:"flex gap-2",children:(0,o.jsx)(ex,{id:"bulk-brand",placeholder:"e.g. Zara",onChange:e=>f(e.target.value)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(eg.Label,{htmlFor:"bulk-price",children:"Set Price for All"}),(0,o.jsx)("div",{className:"flex gap-2",children:(0,o.jsx)(ex,{id:"bulk-price",type:"number",placeholder:"15000",onChange:e=>v(e.target.value)})})]})]})})]}),r.length>0&&(0,o.jsxs)(as,{variant:"destructive",children:[(0,o.jsx)(au,{className:"h-4 w-4"}),(0,o.jsx)(ap,{children:(0,o.jsx)("div",{className:"space-y-1",children:r.map((e,t)=>(0,o.jsx)("div",{children:e},t))})})]}),t.length>0&&(0,o.jsxs)(eh,{children:[(0,o.jsxs)(eb,{children:[(0,o.jsx)(ey,{children:"Item Details"}),(0,o.jsx)(ew,{children:"Fill in the details for each item"})]}),(0,o.jsxs)(ej,{children:[(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full border-collapse",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"border-b",children:[(0,o.jsx)("th",{className:"text-left p-2 font-medium",children:"Image"}),(0,o.jsx)("th",{className:"text-left p-2 font-medium",children:"Name *"}),(0,o.jsx)("th",{className:"text-left p-2 font-medium",children:"Brand"}),(0,o.jsx)("th",{className:"text-left p-2 font-medium",children:"Size"}),(0,o.jsx)("th",{className:"text-left p-2 font-medium",children:"Price (₦) *"}),(0,o.jsx)("th",{className:"text-left p-2 font-medium",children:"Actions"})]})}),(0,o.jsx)("tbody",{children:t.map((e,t)=>(0,o.jsxs)("tr",{className:"border-b",children:[(0,o.jsx)("td",{className:"p-2",children:(0,o.jsx)("div",{className:"relative w-16 h-16",children:(0,o.jsx)(ag(),{src:e.imageUrl,alt:`Item ${t+1}`,fill:!0,className:"object-cover rounded"})})}),(0,o.jsx)("td",{className:"p-2",children:(0,o.jsx)(ex,{value:e.name,onChange:e=>u(t,"name",e.target.value),placeholder:"e.g. Red Floral Dress",className:"min-w-[200px]"})}),(0,o.jsx)("td",{className:"p-2",children:(0,o.jsx)(ex,{value:e.brand,onChange:e=>u(t,"brand",e.target.value),placeholder:"e.g. Zara",className:"min-w-[120px]"})}),(0,o.jsx)("td",{className:"p-2",children:(0,o.jsxs)(at,{value:e.size,onValueChange:e=>u(t,"size",e),children:[(0,o.jsx)(ai,{className:"min-w-[100px]",children:(0,o.jsx)(an,{placeholder:"Size"})}),(0,o.jsxs)(aa,{children:[(0,o.jsx)(ar,{value:"XS",children:"XS"}),(0,o.jsx)(ar,{value:"S",children:"S"}),(0,o.jsx)(ar,{value:"M",children:"M"}),(0,o.jsx)(ar,{value:"L",children:"L"}),(0,o.jsx)(ar,{value:"XL",children:"XL"}),(0,o.jsx)(ar,{value:"XXL",children:"XXL"})]})]})}),(0,o.jsx)("td",{className:"p-2",children:(0,o.jsx)(ex,{type:"number",value:e.price,onChange:e=>u(t,"price",e.target.value),placeholder:"15000",className:"min-w-[120px]"})}),(0,o.jsx)("td",{className:"p-2",children:(0,o.jsx)(ev,{variant:"outline",size:"sm",onClick:()=>m(t),children:(0,o.jsx)(am,{className:"h-4 w-4"})})})]},e.id))})]})}),(0,o.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,o.jsxs)("p",{className:"text-sm text-gray-600",children:[t.length," item",1!==t.length?"s":""," ready to save"]}),(0,o.jsxs)("div",{className:"flex gap-3",children:[(0,o.jsxs)(ev,{variant:"outline",children:[(0,o.jsx)(af,{className:"h-4 w-4 mr-2"}),"Save as Draft"]}),(0,o.jsxs)(ev,{onClick:g,disabled:i,children:[(0,o.jsx)(av,{className:"h-4 w-4 mr-2"}),i?"Saving...":"Save Items"]})]})]})]})]})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,n)=>{"use strict";n.d(t,{Label:()=>o});var i=n(60687);n(43210);var a=n(78148),r=n(4780);function o({className:e,...t}){return(0,i.jsx)(a.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},84031:(e,t,n)=>{"use strict";var i=n(34452);function a(){}function r(){}r.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,r,o){if(o!==i){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:r,resetWarningCache:a};return n.PropTypes=n,n}},86897:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return s},permanentRedirect:function(){return c},redirect:function(){return l}});let i=n(52836),a=n(49026),r=n(19121).actionAsyncStorage;function o(e,t,n){void 0===n&&(n=i.RedirectStatusCode.TemporaryRedirect);let r=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+n+";",r}function l(e,t){var n;throw null!=t||(t=(null==r||null==(n=r.getStore())?void 0:n.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,i.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,i.RedirectStatusCode.PermanentRedirect)}function s(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function p(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87955:(e,t,n)=>{e.exports=n(84031)()},89513:(e,t,n)=>{"use strict";e.exports=n(94041).vendored.contexts.HeadManagerContext},91645:e=>{"use strict";e.exports=require("net")},94431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,metadata:()=>c});var i=n(37413),a=n(22376),r=n.n(a),o=n(68726),l=n.n(o);n(61135);let c={title:"Create Next App",description:"Generated by create next app"};function s({children:e}){return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{className:`${r().variable} ${l().variable} antialiased`,children:e})})}},94735:e=>{"use strict";e.exports=require("events")},97576:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ReadonlyURLSearchParams:function(){return p},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return r.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let i=n(86897),a=n(49026),r=n(62765),o=n(48976),l=n(70899),c=n(163);class s extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class p extends URLSearchParams{append(){throw new s}delete(){throw new s}set(){throw new s}sort(){throw new s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[447,145,875,658,923,805,21],()=>n(69699));module.exports=i})();