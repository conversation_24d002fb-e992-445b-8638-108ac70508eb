-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clothing_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.instagram_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_memory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tool_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_analytics ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Clothing items policies
CREATE POLICY "Users can view own clothing items" ON public.clothing_items
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own clothing items" ON public.clothing_items
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own clothing items" ON public.clothing_items
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own clothing items" ON public.clothing_items
  FOR DELETE USING (auth.uid() = user_id);

-- Customers policies
CREATE POLICY "Users can view own customers" ON public.customers
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own customers" ON public.customers
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own customers" ON public.customers
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own customers" ON public.customers
  FOR DELETE USING (auth.uid() = user_id);

-- Instagram posts policies
CREATE POLICY "Users can view own instagram posts" ON public.instagram_posts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own instagram posts" ON public.instagram_posts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own instagram posts" ON public.instagram_posts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own instagram posts" ON public.instagram_posts
  FOR DELETE USING (auth.uid() = user_id);

-- Conversations policies
CREATE POLICY "Users can view own conversations" ON public.conversations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own conversations" ON public.conversations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own conversations" ON public.conversations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own conversations" ON public.conversations
  FOR DELETE USING (auth.uid() = user_id);

-- Sales policies
CREATE POLICY "Users can view own sales" ON public.sales
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sales" ON public.sales
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sales" ON public.sales
  FOR UPDATE USING (auth.uid() = user_id);

-- Automation rules policies
CREATE POLICY "Users can view own automation rules" ON public.automation_rules
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own automation rules" ON public.automation_rules
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own automation rules" ON public.automation_rules
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own automation rules" ON public.automation_rules
  FOR DELETE USING (auth.uid() = user_id);

-- Agent sessions policies
CREATE POLICY "Users can view own agent sessions" ON public.agent_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own agent sessions" ON public.agent_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own agent sessions" ON public.agent_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- Agent memory policies (linked through agent sessions)
CREATE POLICY "Users can view own agent memory" ON public.agent_memory
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = agent_memory.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own agent memory" ON public.agent_memory
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = agent_memory.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own agent memory" ON public.agent_memory
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = agent_memory.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );

-- Workflow executions policies
CREATE POLICY "Users can view own workflow executions" ON public.workflow_executions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own workflow executions" ON public.workflow_executions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own workflow executions" ON public.workflow_executions
  FOR UPDATE USING (auth.uid() = user_id);

-- Tool usage policies (linked through agent sessions)
CREATE POLICY "Users can view own tool usage" ON public.tool_usage
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = tool_usage.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own tool usage" ON public.tool_usage
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = tool_usage.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );

-- Analytics policies
CREATE POLICY "Users can view own post analytics" ON public.post_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.instagram_posts 
      WHERE instagram_posts.id = post_analytics.post_id 
      AND instagram_posts.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own post analytics" ON public.post_analytics
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.instagram_posts 
      WHERE instagram_posts.id = post_analytics.post_id 
      AND instagram_posts.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view own inventory analytics" ON public.inventory_analytics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own inventory analytics" ON public.inventory_analytics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own agent analytics" ON public.agent_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = agent_analytics.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own agent analytics" ON public.agent_analytics
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.agent_sessions 
      WHERE agent_sessions.id = agent_analytics.agent_id 
      AND agent_sessions.user_id = auth.uid()
    )
  );
