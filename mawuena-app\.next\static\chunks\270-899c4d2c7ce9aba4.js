(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[270],{721:(e,t,n)=>{"use strict";n.d(t,{UC:()=>n0,In:()=>nJ,q7:()=>n2,VF:()=>n4,p4:()=>n3,ZL:()=>nQ,bL:()=>nY,wn:()=>n5,PP:()=>n6,l9:()=>n$,WT:()=>nZ,LM:()=>n1});var i,a,o,r=n(2115),l=n(7650);function c(e,[t,n]){return Math.min(n,Math.max(t,e))}var p=n(5185),s=n(7328),d=n(6101),u=n(6081),m=n(4315),f=n(3655),v=n(9033),x=n(5155),g="dismissableLayer.update",h=r.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=r.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:s,onInteractOutside:u,onDismiss:m,...b}=e,k=r.useContext(h),[j,E]=r.useState(null),S=null!=(i=null==j?void 0:j.ownerDocument)?i:null==(n=globalThis)?void 0:n.document,[,C]=r.useState({}),O=(0,d.s)(t,e=>E(e)),A=Array.from(k.layers),[P]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),R=A.indexOf(P),z=j?A.indexOf(j):-1,D=k.layersWithOutsidePointerEventsDisabled.size>0,T=z>=R,L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,i=(0,v.c)(e),a=r.useRef(!1),o=r.useRef(()=>{});return r.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){w("dismissableLayer.pointerDownOutside",i,a,{discrete:!0})},a={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",o.current),o.current=t,n.addEventListener("click",o.current,{once:!0})):t()}else n.removeEventListener("click",o.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",o.current)}},[n,i]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...k.branches].some(e=>e.contains(t));T&&!n&&(null==c||c(e),null==u||u(e),e.defaultPrevented||null==m||m())},S),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,i=(0,v.c)(e),a=r.useRef(!1);return r.useEffect(()=>{let e=e=>{e.target&&!a.current&&w("dismissableLayer.focusOutside",i,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,i]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==s||s(e),null==u||u(e),e.defaultPrevented||null==m||m())},S);return!function(e,t=globalThis?.document){let n=(0,v.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{z===k.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},S),r.useEffect(()=>{if(j)return o&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(a=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(j)),k.layers.add(j),y(),()=>{o&&1===k.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=a)}},[j,S,o,k]),r.useEffect(()=>()=>{j&&(k.layers.delete(j),k.layersWithOutsidePointerEventsDisabled.delete(j),y())},[j,k]),r.useEffect(()=>{let e=()=>C({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,x.jsx)(f.sG.div,{...b,ref:O,style:{pointerEvents:D?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,p.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,p.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function y(){let e=new CustomEvent(g);document.dispatchEvent(e)}function w(e,t,n,i){let{discrete:a}=i,o=n.originalEvent.target,r=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),a?(0,f.hO)(o,r):o.dispatchEvent(r)}b.displayName="DismissableLayer",r.forwardRef((e,t)=>{let n=r.useContext(h),i=r.useRef(null),a=(0,d.s)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,x.jsx)(f.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch";var k=0;function j(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var E="focusScope.autoFocusOnMount",S="focusScope.autoFocusOnUnmount",C={bubbles:!1,cancelable:!0},O=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:o,...l}=e,[c,p]=r.useState(null),s=(0,v.c)(a),u=(0,v.c)(o),m=r.useRef(null),g=(0,d.s)(t,e=>p(e)),h=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(i){let e=function(e){if(h.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:R(m.current,{select:!0})},t=function(e){if(h.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||R(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&R(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[i,c,h.paused]),r.useEffect(()=>{if(c){z.add(h);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(E,C);c.addEventListener(E,s),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let i of e)if(R(i,{select:t}),document.activeElement!==n)return}(A(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&R(c))}return()=>{c.removeEventListener(E,s),setTimeout(()=>{let t=new CustomEvent(S,C);c.addEventListener(S,u),c.dispatchEvent(t),t.defaultPrevented||R(null!=e?e:document.body,{select:!0}),c.removeEventListener(S,u),z.remove(h)},0)}}},[c,s,u,h]);let b=r.useCallback(e=>{if(!n&&!i||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[i,o]=function(e){let t=A(e);return[P(t,e),P(t.reverse(),e)]}(t);i&&o?e.shiftKey||a!==o?e.shiftKey&&a===i&&(e.preventDefault(),n&&R(o,{select:!0})):(e.preventDefault(),n&&R(i,{select:!0})):a===t&&e.preventDefault()}},[n,i,h.paused]);return(0,x.jsx)(f.sG.div,{tabIndex:-1,...l,ref:g,onKeyDown:b})});function A(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function P(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function R(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let i=document.activeElement;e.focus({preventScroll:!0}),e!==i&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}O.displayName="FocusScope";var z=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=D(e,t)).unshift(t)},remove(t){var n;null==(n=(e=D(e,t))[0])||n.resume()}}}();function D(e,t){let n=[...e],i=n.indexOf(t);return -1!==i&&n.splice(i,1),n}var T=n(1285);let L=["top","right","bottom","left"],M=Math.min,_=Math.max,F=Math.round,N=Math.floor,I=e=>({x:e,y:e}),q={left:"right",right:"left",bottom:"top",top:"bottom"},H={start:"end",end:"start"};function B(e,t){return"function"==typeof e?e(t):e}function W(e){return e.split("-")[0]}function V(e){return e.split("-")[1]}function G(e){return"x"===e?"y":"x"}function U(e){return"y"===e?"height":"width"}function K(e){return["top","bottom"].includes(W(e))?"y":"x"}function X(e){return e.replace(/start|end/g,e=>H[e])}function Y(e){return e.replace(/left|right|bottom|top/g,e=>q[e])}function $(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function Z(e){let{x:t,y:n,width:i,height:a}=e;return{width:i,height:a,top:n,left:t,right:t+i,bottom:n+a,x:t,y:n}}function J(e,t,n){let i,{reference:a,floating:o}=e,r=K(t),l=G(K(t)),c=U(l),p=W(t),s="y"===r,d=a.x+a.width/2-o.width/2,u=a.y+a.height/2-o.height/2,m=a[c]/2-o[c]/2;switch(p){case"top":i={x:d,y:a.y-o.height};break;case"bottom":i={x:d,y:a.y+a.height};break;case"right":i={x:a.x+a.width,y:u};break;case"left":i={x:a.x-o.width,y:u};break;default:i={x:a.x,y:a.y}}switch(V(t)){case"start":i[l]-=m*(n&&s?-1:1);break;case"end":i[l]+=m*(n&&s?-1:1)}return i}let Q=async(e,t,n)=>{let{placement:i="bottom",strategy:a="absolute",middleware:o=[],platform:r}=n,l=o.filter(Boolean),c=await (null==r.isRTL?void 0:r.isRTL(t)),p=await r.getElementRects({reference:e,floating:t,strategy:a}),{x:s,y:d}=J(p,i,c),u=i,m={},f=0;for(let n=0;n<l.length;n++){let{name:o,fn:v}=l[n],{x:x,y:g,data:h,reset:b}=await v({x:s,y:d,initialPlacement:i,placement:u,strategy:a,middlewareData:m,rects:p,platform:r,elements:{reference:e,floating:t}});s=null!=x?x:s,d=null!=g?g:d,m={...m,[o]:{...m[o],...h}},b&&f<=50&&(f++,"object"==typeof b&&(b.placement&&(u=b.placement),b.rects&&(p=!0===b.rects?await r.getElementRects({reference:e,floating:t,strategy:a}):b.rects),{x:s,y:d}=J(p,u,c)),n=-1)}return{x:s,y:d,placement:u,strategy:a,middlewareData:m}};async function ee(e,t){var n;void 0===t&&(t={});let{x:i,y:a,platform:o,rects:r,elements:l,strategy:c}=e,{boundary:p="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:u=!1,padding:m=0}=B(t,e),f=$(m),v=l[u?"floating"===d?"reference":"floating":d],x=Z(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(l.floating)),boundary:p,rootBoundary:s,strategy:c})),g="floating"===d?{x:i,y:a,width:r.floating.width,height:r.floating.height}:r.reference,h=await (null==o.getOffsetParent?void 0:o.getOffsetParent(l.floating)),b=await (null==o.isElement?void 0:o.isElement(h))&&await (null==o.getScale?void 0:o.getScale(h))||{x:1,y:1},y=Z(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:h,strategy:c}):g);return{top:(x.top-y.top+f.top)/b.y,bottom:(y.bottom-x.bottom+f.bottom)/b.y,left:(x.left-y.left+f.left)/b.x,right:(y.right-x.right+f.right)/b.x}}function et(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function en(e){return L.some(t=>e[t]>=0)}async function ei(e,t){let{placement:n,platform:i,elements:a}=e,o=await (null==i.isRTL?void 0:i.isRTL(a.floating)),r=W(n),l=V(n),c="y"===K(n),p=["left","top"].includes(r)?-1:1,s=o&&c?-1:1,d=B(t,e),{mainAxis:u,crossAxis:m,alignmentAxis:f}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof f&&(m="end"===l?-1*f:f),c?{x:m*s,y:u*p}:{x:u*p,y:m*s}}function ea(){return"undefined"!=typeof window}function eo(e){return ec(e)?(e.nodeName||"").toLowerCase():"#document"}function er(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function el(e){var t;return null==(t=(ec(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ec(e){return!!ea()&&(e instanceof Node||e instanceof er(e).Node)}function ep(e){return!!ea()&&(e instanceof Element||e instanceof er(e).Element)}function es(e){return!!ea()&&(e instanceof HTMLElement||e instanceof er(e).HTMLElement)}function ed(e){return!!ea()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof er(e).ShadowRoot)}function eu(e){let{overflow:t,overflowX:n,overflowY:i,display:a}=eg(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(a)}function em(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ef(e){let t=ev(),n=ep(e)?eg(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ev(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ex(e){return["html","body","#document"].includes(eo(e))}function eg(e){return er(e).getComputedStyle(e)}function eh(e){return ep(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eb(e){if("html"===eo(e))return e;let t=e.assignedSlot||e.parentNode||ed(e)&&e.host||el(e);return ed(t)?t.host:t}function ey(e,t,n){var i;void 0===t&&(t=[]),void 0===n&&(n=!0);let a=function e(t){let n=eb(t);return ex(n)?t.ownerDocument?t.ownerDocument.body:t.body:es(n)&&eu(n)?n:e(n)}(e),o=a===(null==(i=e.ownerDocument)?void 0:i.body),r=er(a);if(o){let e=ew(r);return t.concat(r,r.visualViewport||[],eu(a)?a:[],e&&n?ey(e):[])}return t.concat(a,ey(a,[],n))}function ew(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ek(e){let t=eg(e),n=parseFloat(t.width)||0,i=parseFloat(t.height)||0,a=es(e),o=a?e.offsetWidth:n,r=a?e.offsetHeight:i,l=F(n)!==o||F(i)!==r;return l&&(n=o,i=r),{width:n,height:i,$:l}}function ej(e){return ep(e)?e:e.contextElement}function eE(e){let t=ej(e);if(!es(t))return I(1);let n=t.getBoundingClientRect(),{width:i,height:a,$:o}=ek(t),r=(o?F(n.width):n.width)/i,l=(o?F(n.height):n.height)/a;return r&&Number.isFinite(r)||(r=1),l&&Number.isFinite(l)||(l=1),{x:r,y:l}}let eS=I(0);function eC(e){let t=er(e);return ev()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eS}function eO(e,t,n,i){var a;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),r=ej(e),l=I(1);t&&(i?ep(i)&&(l=eE(i)):l=eE(e));let c=(void 0===(a=n)&&(a=!1),i&&(!a||i===er(r))&&a)?eC(r):I(0),p=(o.left+c.x)/l.x,s=(o.top+c.y)/l.y,d=o.width/l.x,u=o.height/l.y;if(r){let e=er(r),t=i&&ep(i)?er(i):i,n=e,a=ew(n);for(;a&&i&&t!==n;){let e=eE(a),t=a.getBoundingClientRect(),i=eg(a),o=t.left+(a.clientLeft+parseFloat(i.paddingLeft))*e.x,r=t.top+(a.clientTop+parseFloat(i.paddingTop))*e.y;p*=e.x,s*=e.y,d*=e.x,u*=e.y,p+=o,s+=r,a=ew(n=er(a))}}return Z({width:d,height:u,x:p,y:s})}function eA(e,t){let n=eh(e).scrollLeft;return t?t.left+n:eO(el(e)).left+n}function eP(e,t,n){void 0===n&&(n=!1);let i=e.getBoundingClientRect();return{x:i.left+t.scrollLeft-(n?0:eA(e,i)),y:i.top+t.scrollTop}}function eR(e,t,n){let i;if("viewport"===t)i=function(e,t){let n=er(e),i=el(e),a=n.visualViewport,o=i.clientWidth,r=i.clientHeight,l=0,c=0;if(a){o=a.width,r=a.height;let e=ev();(!e||e&&"fixed"===t)&&(l=a.offsetLeft,c=a.offsetTop)}return{width:o,height:r,x:l,y:c}}(e,n);else if("document"===t)i=function(e){let t=el(e),n=eh(e),i=e.ownerDocument.body,a=_(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),o=_(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight),r=-n.scrollLeft+eA(e),l=-n.scrollTop;return"rtl"===eg(i).direction&&(r+=_(t.clientWidth,i.clientWidth)-a),{width:a,height:o,x:r,y:l}}(el(e));else if(ep(t))i=function(e,t){let n=eO(e,!0,"fixed"===t),i=n.top+e.clientTop,a=n.left+e.clientLeft,o=es(e)?eE(e):I(1),r=e.clientWidth*o.x,l=e.clientHeight*o.y;return{width:r,height:l,x:a*o.x,y:i*o.y}}(t,n);else{let n=eC(e);i={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Z(i)}function ez(e){return"static"===eg(e).position}function eD(e,t){if(!es(e)||"fixed"===eg(e).position)return null;if(t)return t(e);let n=e.offsetParent;return el(e)===n&&(n=n.ownerDocument.body),n}function eT(e,t){let n=er(e);if(em(e))return n;if(!es(e)){let t=eb(e);for(;t&&!ex(t);){if(ep(t)&&!ez(t))return t;t=eb(t)}return n}let i=eD(e,t);for(;i&&["table","td","th"].includes(eo(i))&&ez(i);)i=eD(i,t);return i&&ex(i)&&ez(i)&&!ef(i)?n:i||function(e){let t=eb(e);for(;es(t)&&!ex(t);){if(ef(t))return t;if(em(t))break;t=eb(t)}return null}(e)||n}let eL=async function(e){let t=this.getOffsetParent||eT,n=this.getDimensions,i=await n(e.floating);return{reference:function(e,t,n){let i=es(t),a=el(t),o="fixed"===n,r=eO(e,!0,o,t),l={scrollLeft:0,scrollTop:0},c=I(0);if(i||!i&&!o)if(("body"!==eo(t)||eu(a))&&(l=eh(t)),i){let e=eO(t,!0,o,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else a&&(c.x=eA(a));o&&!i&&a&&(c.x=eA(a));let p=!a||i||o?I(0):eP(a,l);return{x:r.left+l.scrollLeft-c.x-p.x,y:r.top+l.scrollTop-c.y-p.y,width:r.width,height:r.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},eM={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:i,strategy:a}=e,o="fixed"===a,r=el(i),l=!!t&&em(t.floating);if(i===r||l&&o)return n;let c={scrollLeft:0,scrollTop:0},p=I(1),s=I(0),d=es(i);if((d||!d&&!o)&&(("body"!==eo(i)||eu(r))&&(c=eh(i)),es(i))){let e=eO(i);p=eE(i),s.x=e.x+i.clientLeft,s.y=e.y+i.clientTop}let u=!r||d||o?I(0):eP(r,c,!0);return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-c.scrollLeft*p.x+s.x+u.x,y:n.y*p.y-c.scrollTop*p.y+s.y+u.y}},getDocumentElement:el,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:a}=e,o=[..."clippingAncestors"===n?em(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let i=ey(e,[],!1).filter(e=>ep(e)&&"body"!==eo(e)),a=null,o="fixed"===eg(e).position,r=o?eb(e):e;for(;ep(r)&&!ex(r);){let t=eg(r),n=ef(r);n||"fixed"!==t.position||(a=null),(o?!n&&!a:!n&&"static"===t.position&&!!a&&["absolute","fixed"].includes(a.position)||eu(r)&&!n&&function e(t,n){let i=eb(t);return!(i===n||!ep(i)||ex(i))&&("fixed"===eg(i).position||e(i,n))}(e,r))?i=i.filter(e=>e!==r):a=t,r=eb(r)}return t.set(e,i),i}(t,this._c):[].concat(n),i],r=o[0],l=o.reduce((e,n)=>{let i=eR(t,n,a);return e.top=_(i.top,e.top),e.right=M(i.right,e.right),e.bottom=M(i.bottom,e.bottom),e.left=_(i.left,e.left),e},eR(t,r,a));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eT,getElementRects:eL,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ek(e);return{width:t,height:n}},getScale:eE,isElement:ep,isRTL:function(e){return"rtl"===eg(e).direction}};function e_(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eF=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:a,rects:o,platform:r,elements:l,middlewareData:c}=t,{element:p,padding:s=0}=B(e,t)||{};if(null==p)return{};let d=$(s),u={x:n,y:i},m=G(K(a)),f=U(m),v=await r.getDimensions(p),x="y"===m,g=x?"clientHeight":"clientWidth",h=o.reference[f]+o.reference[m]-u[m]-o.floating[f],b=u[m]-o.reference[m],y=await (null==r.getOffsetParent?void 0:r.getOffsetParent(p)),w=y?y[g]:0;w&&await (null==r.isElement?void 0:r.isElement(y))||(w=l.floating[g]||o.floating[f]);let k=w/2-v[f]/2-1,j=M(d[x?"top":"left"],k),E=M(d[x?"bottom":"right"],k),S=w-v[f]-E,C=w/2-v[f]/2+(h/2-b/2),O=_(j,M(C,S)),A=!c.arrow&&null!=V(a)&&C!==O&&o.reference[f]/2-(C<j?j:E)-v[f]/2<0,P=A?C<j?C-j:C-S:0;return{[m]:u[m]+P,data:{[m]:O,centerOffset:C-O-P,...A&&{alignmentOffset:P}},reset:A}}}),eN=(e,t,n)=>{let i=new Map,a={platform:eM,...n},o={...a.platform,_c:i};return Q(e,t,{...a,platform:o})};var eI="undefined"!=typeof document?r.useLayoutEffect:function(){};function eq(e,t){let n,i,a;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(i=n;0!=i--;)if(!eq(e[i],t[i]))return!1;return!0}if((n=(a=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(i=n;0!=i--;)if(!({}).hasOwnProperty.call(t,a[i]))return!1;for(i=n;0!=i--;){let n=a[i];if(("_owner"!==n||!e.$$typeof)&&!eq(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eH(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eB(e,t){let n=eH(e);return Math.round(t*n)/n}function eW(e){let t=r.useRef(e);return eI(()=>{t.current=e}),t}let eV=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eF({element:n.current,padding:i}).fn(t):{}:n?eF({element:n,padding:i}).fn(t):{}}}),eG=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,i;let{x:a,y:o,placement:r,middlewareData:l}=t,c=await ei(t,e);return r===(null==(n=l.offset)?void 0:n.placement)&&null!=(i=l.arrow)&&i.alignmentOffset?{}:{x:a+c.x,y:o+c.y,data:{...c,placement:r}}}}}(e),options:[e,t]}),eU=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:a}=t,{mainAxis:o=!0,crossAxis:r=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=B(e,t),p={x:n,y:i},s=await ee(t,c),d=K(W(a)),u=G(d),m=p[u],f=p[d];if(o){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",n=m+s[e],i=m-s[t];m=_(n,M(m,i))}if(r){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=f+s[e],i=f-s[t];f=_(n,M(f,i))}let v=l.fn({...t,[u]:m,[d]:f});return{...v,data:{x:v.x-n,y:v.y-i,enabled:{[u]:o,[d]:r}}}}}}(e),options:[e,t]}),eK=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:i,placement:a,rects:o,middlewareData:r}=t,{offset:l=0,mainAxis:c=!0,crossAxis:p=!0}=B(e,t),s={x:n,y:i},d=K(a),u=G(d),m=s[u],f=s[d],v=B(l,t),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(c){let e="y"===u?"height":"width",t=o.reference[u]-o.floating[e]+x.mainAxis,n=o.reference[u]+o.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(p){var g,h;let e="y"===u?"width":"height",t=["top","left"].includes(W(a)),n=o.reference[d]-o.floating[e]+(t&&(null==(g=r.offset)?void 0:g[d])||0)+(t?0:x.crossAxis),i=o.reference[d]+o.reference[e]+(t?0:(null==(h=r.offset)?void 0:h[d])||0)-(t?x.crossAxis:0);f<n?f=n:f>i&&(f=i)}return{[u]:m,[d]:f}}}}(e),options:[e,t]}),eX=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,i,a,o,r;let{placement:l,middlewareData:c,rects:p,initialPlacement:s,platform:d,elements:u}=t,{mainAxis:m=!0,crossAxis:f=!0,fallbackPlacements:v,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:h=!0,...b}=B(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let y=W(l),w=K(s),k=W(s)===s,j=await (null==d.isRTL?void 0:d.isRTL(u.floating)),E=v||(k||!h?[Y(s)]:function(e){let t=Y(e);return[X(e),t,X(t)]}(s)),S="none"!==g;!v&&S&&E.push(...function(e,t,n,i){let a=V(e),o=function(e,t,n){let i=["left","right"],a=["right","left"];switch(e){case"top":case"bottom":if(n)return t?a:i;return t?i:a;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(W(e),"start"===n,i);return a&&(o=o.map(e=>e+"-"+a),t&&(o=o.concat(o.map(X)))),o}(s,h,g,j));let C=[s,...E],O=await ee(t,b),A=[],P=(null==(i=c.flip)?void 0:i.overflows)||[];if(m&&A.push(O[y]),f){let e=function(e,t,n){void 0===n&&(n=!1);let i=V(e),a=G(K(e)),o=U(a),r="x"===a?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return t.reference[o]>t.floating[o]&&(r=Y(r)),[r,Y(r)]}(l,p,j);A.push(O[e[0]],O[e[1]])}if(P=[...P,{placement:l,overflows:A}],!A.every(e=>e<=0)){let e=((null==(a=c.flip)?void 0:a.index)||0)+1,t=C[e];if(t&&("alignment"!==f||w===K(t)||P.every(e=>e.overflows[0]>0&&K(e.placement)===w)))return{data:{index:e,overflows:P},reset:{placement:t}};let n=null==(o=P.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(x){case"bestFit":{let e=null==(r=P.filter(e=>{if(S){let t=K(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:r[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eY=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,i;let a,o,{placement:r,rects:l,platform:c,elements:p}=t,{apply:s=()=>{},...d}=B(e,t),u=await ee(t,d),m=W(r),f=V(r),v="y"===K(r),{width:x,height:g}=l.floating;"top"===m||"bottom"===m?(a=m,o=f===(await (null==c.isRTL?void 0:c.isRTL(p.floating))?"start":"end")?"left":"right"):(o=m,a="end"===f?"top":"bottom");let h=g-u.top-u.bottom,b=x-u.left-u.right,y=M(g-u[a],h),w=M(x-u[o],b),k=!t.middlewareData.shift,j=y,E=w;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(E=b),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(j=h),k&&!f){let e=_(u.left,0),t=_(u.right,0),n=_(u.top,0),i=_(u.bottom,0);v?E=x-2*(0!==e||0!==t?e+t:_(u.left,u.right)):j=g-2*(0!==n||0!==i?n+i:_(u.top,u.bottom))}await s({...t,availableWidth:E,availableHeight:j});let S=await c.getDimensions(p.floating);return x!==S.width||g!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e$=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:i="referenceHidden",...a}=B(e,t);switch(i){case"referenceHidden":{let e=et(await ee(t,{...a,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:en(e)}}}case"escaped":{let e=et(await ee(t,{...a,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:en(e)}}}default:return{}}}}}(e),options:[e,t]}),eZ=(e,t)=>({...eV(e),options:[e,t]});var eJ=r.forwardRef((e,t)=>{let{children:n,width:i=10,height:a=5,...o}=e;return(0,x.jsx)(f.sG.svg,{...o,ref:t,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,x.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eJ.displayName="Arrow";var eQ=n(2712),e0="Popper",[e1,e2]=(0,u.A)(e0),[e3,e4]=e1(e0),e6=e=>{let{__scopePopper:t,children:n}=e,[i,a]=r.useState(null);return(0,x.jsx)(e3,{scope:t,anchor:i,onAnchorChange:a,children:n})};e6.displayName=e0;var e5="PopperAnchor",e8=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...a}=e,o=e4(e5,n),l=r.useRef(null),c=(0,d.s)(t,l);return r.useEffect(()=>{o.onAnchorChange((null==i?void 0:i.current)||l.current)}),i?null:(0,x.jsx)(f.sG.div,{...a,ref:c})});e8.displayName=e5;var e9="PopperContent",[e7,te]=e1(e9),tt=r.forwardRef((e,t)=>{var n,i,a,o,c,p,s,u;let{__scopePopper:m,side:g="bottom",sideOffset:h=0,align:b="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:k=!0,collisionBoundary:j=[],collisionPadding:E=0,sticky:S="partial",hideWhenDetached:C=!1,updatePositionStrategy:O="optimized",onPlaced:A,...P}=e,R=e4(e9,m),[z,D]=r.useState(null),T=(0,d.s)(t,e=>D(e)),[L,F]=r.useState(null),I=function(e){let[t,n]=r.useState(void 0);return(0,eQ.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let i,a;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(L),q=null!=(s=null==I?void 0:I.width)?s:0,H=null!=(u=null==I?void 0:I.height)?u:0,B="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},W=Array.isArray(j)?j:[j],V=W.length>0,G={padding:B,boundary:W.filter(to),altBoundary:V},{refs:U,floatingStyles:K,placement:X,isPositioned:Y,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:a,elements:{reference:o,floating:c}={},transform:p=!0,whileElementsMounted:s,open:d}=e,[u,m]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,v]=r.useState(i);eq(f,i)||v(i);let[x,g]=r.useState(null),[h,b]=r.useState(null),y=r.useCallback(e=>{e!==E.current&&(E.current=e,g(e))},[]),w=r.useCallback(e=>{e!==S.current&&(S.current=e,b(e))},[]),k=o||x,j=c||h,E=r.useRef(null),S=r.useRef(null),C=r.useRef(u),O=null!=s,A=eW(s),P=eW(a),R=eW(d),z=r.useCallback(()=>{if(!E.current||!S.current)return;let e={placement:t,strategy:n,middleware:f};P.current&&(e.platform=P.current),eN(E.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};D.current&&!eq(C.current,t)&&(C.current=t,l.flushSync(()=>{m(t)}))})},[f,t,n,P,R]);eI(()=>{!1===d&&C.current.isPositioned&&(C.current.isPositioned=!1,m(e=>({...e,isPositioned:!1})))},[d]);let D=r.useRef(!1);eI(()=>(D.current=!0,()=>{D.current=!1}),[]),eI(()=>{if(k&&(E.current=k),j&&(S.current=j),k&&j){if(A.current)return A.current(k,j,z);z()}},[k,j,z,A,O]);let T=r.useMemo(()=>({reference:E,floating:S,setReference:y,setFloating:w}),[y,w]),L=r.useMemo(()=>({reference:k,floating:j}),[k,j]),M=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=eB(L.floating,u.x),i=eB(L.floating,u.y);return p?{...e,transform:"translate("+t+"px, "+i+"px)",...eH(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:i}},[n,p,L.floating,u.x,u.y]);return r.useMemo(()=>({...u,update:z,refs:T,elements:L,floatingStyles:M}),[u,z,T,L,M])}({strategy:"fixed",placement:g+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,i){let a;void 0===i&&(i={});let{ancestorScroll:o=!0,ancestorResize:r=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:p=!1}=i,s=ej(e),d=o||r?[...s?ey(s):[],...ey(t)]:[];d.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),r&&e.addEventListener("resize",n)});let u=s&&c?function(e,t){let n,i=null,a=el(e);function o(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function r(l,c){void 0===l&&(l=!1),void 0===c&&(c=1),o();let p=e.getBoundingClientRect(),{left:s,top:d,width:u,height:m}=p;if(l||t(),!u||!m)return;let f=N(d),v=N(a.clientWidth-(s+u)),x={rootMargin:-f+"px "+-v+"px "+-N(a.clientHeight-(d+m))+"px "+-N(s)+"px",threshold:_(0,M(1,c))||1},g=!0;function h(t){let i=t[0].intersectionRatio;if(i!==c){if(!g)return r();i?r(!1,i):n=setTimeout(()=>{r(!1,1e-7)},1e3)}1!==i||e_(p,e.getBoundingClientRect())||r(),g=!1}try{i=new IntersectionObserver(h,{...x,root:a.ownerDocument})}catch(e){i=new IntersectionObserver(h,x)}i.observe(e)}(!0),o}(s,n):null,m=-1,f=null;l&&(f=new ResizeObserver(e=>{let[i]=e;i&&i.target===s&&f&&(f.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=f)||e.observe(t)})),n()}),s&&!p&&f.observe(s),f.observe(t));let v=p?eO(e):null;return p&&function t(){let i=eO(e);v&&!e_(v,i)&&n(),v=i,a=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{o&&e.removeEventListener("scroll",n),r&&e.removeEventListener("resize",n)}),null==u||u(),null==(e=f)||e.disconnect(),f=null,p&&cancelAnimationFrame(a)}}(...t,{animationFrame:"always"===O})},elements:{reference:R.anchor},middleware:[eG({mainAxis:h+H,alignmentAxis:y}),k&&eU({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?eK():void 0,...G}),k&&eX({...G}),eY({...G,apply:e=>{let{elements:t,rects:n,availableWidth:i,availableHeight:a}=e,{width:o,height:r}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(i,"px")),l.setProperty("--radix-popper-available-height","".concat(a,"px")),l.setProperty("--radix-popper-anchor-width","".concat(o,"px")),l.setProperty("--radix-popper-anchor-height","".concat(r,"px"))}}),L&&eZ({element:L,padding:w}),tr({arrowWidth:q,arrowHeight:H}),C&&e$({strategy:"referenceHidden",...G})]}),[Z,J]=tl(X),Q=(0,v.c)(A);(0,eQ.N)(()=>{Y&&(null==Q||Q())},[Y,Q]);let ee=null==(n=$.arrow)?void 0:n.x,et=null==(i=$.arrow)?void 0:i.y,en=(null==(a=$.arrow)?void 0:a.centerOffset)!==0,[ei,ea]=r.useState();return(0,eQ.N)(()=>{z&&ea(window.getComputedStyle(z).zIndex)},[z]),(0,x.jsx)("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:Y?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ei,"--radix-popper-transform-origin":[null==(o=$.transformOrigin)?void 0:o.x,null==(c=$.transformOrigin)?void 0:c.y].join(" "),...(null==(p=$.hide)?void 0:p.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,x.jsx)(e7,{scope:m,placedSide:Z,onArrowChange:F,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,x.jsx)(f.sG.div,{"data-side":Z,"data-align":J,...P,ref:T,style:{...P.style,animation:Y?void 0:"none"}})})})});tt.displayName=e9;var tn="PopperArrow",ti={top:"bottom",right:"left",bottom:"top",left:"right"},ta=r.forwardRef(function(e,t){let{__scopePopper:n,...i}=e,a=te(tn,n),o=ti[a.placedSide];return(0,x.jsx)("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:(0,x.jsx)(eJ,{...i,ref:t,style:{...i.style,display:"block"}})})});function to(e){return null!==e}ta.displayName=tn;var tr=e=>({name:"transformOrigin",options:e,fn(t){var n,i,a,o,r;let{placement:l,rects:c,middlewareData:p}=t,s=(null==(n=p.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,u=s?0:e.arrowHeight,[m,f]=tl(l),v={start:"0%",center:"50%",end:"100%"}[f],x=(null!=(o=null==(i=p.arrow)?void 0:i.x)?o:0)+d/2,g=(null!=(r=null==(a=p.arrow)?void 0:a.y)?r:0)+u/2,h="",b="";return"bottom"===m?(h=s?v:"".concat(x,"px"),b="".concat(-u,"px")):"top"===m?(h=s?v:"".concat(x,"px"),b="".concat(c.floating.height+u,"px")):"right"===m?(h="".concat(-u,"px"),b=s?v:"".concat(g,"px")):"left"===m&&(h="".concat(c.floating.width+u,"px"),b=s?v:"".concat(g,"px")),{data:{x:h,y:b}}}});function tl(e){let[t,n="center"]=e.split("-");return[t,n]}var tc=r.forwardRef((e,t)=>{var n,i;let{container:a,...o}=e,[c,p]=r.useState(!1);(0,eQ.N)(()=>p(!0),[]);let s=a||c&&(null==(i=globalThis)||null==(n=i.document)?void 0:n.body);return s?l.createPortal((0,x.jsx)(f.sG.div,{...o,ref:t}),s):null});tc.displayName="Portal";var tp=n(9708),ts=n(5845),td=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,x.jsx)(f.sG.span,{...e,ref:t,style:{...td,...e.style}})).displayName="VisuallyHidden";var tu=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tm=new WeakMap,tf=new WeakMap,tv={},tx=0,tg=function(e){return e&&(e.host||tg(e.parentNode))},th=function(e,t,n,i){var a=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tg(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tv[n]||(tv[n]=new WeakMap);var o=tv[n],r=[],l=new Set,c=new Set(a),p=function(e){!e||l.has(e)||(l.add(e),p(e.parentNode))};a.forEach(p);var s=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))s(e);else try{var t=e.getAttribute(i),a=null!==t&&"false"!==t,c=(tm.get(e)||0)+1,p=(o.get(e)||0)+1;tm.set(e,c),o.set(e,p),r.push(e),1===c&&a&&tf.set(e,!0),1===p&&e.setAttribute(n,"true"),a||e.setAttribute(i,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),l.clear(),tx++,function(){r.forEach(function(e){var t=tm.get(e)-1,a=o.get(e)-1;tm.set(e,t),o.set(e,a),t||(tf.has(e)||e.removeAttribute(i),tf.delete(e)),a||e.removeAttribute(n)}),--tx||(tm=new WeakMap,tm=new WeakMap,tf=new WeakMap,tv={})}},tb=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),a=t||tu(e);return a?(i.push.apply(i,Array.from(a.querySelectorAll("[aria-live], script"))),th(i,a,n,"aria-hidden")):function(){return null}},ty=n(9249),tw="right-scroll-bar-position",tk="width-before-scroll-bar";function tj(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tE="undefined"!=typeof window?r.useLayoutEffect:r.useEffect,tS=new WeakMap;function tC(e){return e}var tO=function(e){void 0===e&&(e={});var t,n,i,a,o=(t=null,void 0===n&&(n=tC),i=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=n(e,a);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){a=!0;var t=[];if(i.length){var n=i;i=[],n.forEach(e),t=i}var o=function(){var n=t;t=[],n.forEach(e)},r=function(){return Promise.resolve().then(o)};r(),i={push:function(e){t.push(e),r()},filter:function(e){return t=t.filter(e),i}}}});return o.options=(0,ty.Cl)({async:!0,ssr:!1},e),o}(),tA=function(){},tP=r.forwardRef(function(e,t){var n,i,a,o,l=r.useRef(null),c=r.useState({onScrollCapture:tA,onWheelCapture:tA,onTouchMoveCapture:tA}),p=c[0],s=c[1],d=e.forwardProps,u=e.children,m=e.className,f=e.removeScrollBar,v=e.enabled,x=e.shards,g=e.sideCar,h=e.noRelative,b=e.noIsolation,y=e.inert,w=e.allowPinchZoom,k=e.as,j=e.gapMode,E=(0,ty.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[l,t],i=function(e){return n.forEach(function(t){return tj(t,e)})},(a=(0,r.useState)(function(){return{value:null,callback:i,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=i,o=a.facade,tE(function(){var e=tS.get(o);if(e){var t=new Set(e),i=new Set(n),a=o.current;t.forEach(function(e){i.has(e)||tj(e,null)}),i.forEach(function(e){t.has(e)||tj(e,a)})}tS.set(o,n)},[n]),o),C=(0,ty.Cl)((0,ty.Cl)({},E),p);return r.createElement(r.Fragment,null,v&&r.createElement(g,{sideCar:tO,removeScrollBar:f,shards:x,noRelative:h,noIsolation:b,inert:y,setCallbacks:s,allowPinchZoom:!!w,lockRef:l,gapMode:j}),d?r.cloneElement(r.Children.only(u),(0,ty.Cl)((0,ty.Cl)({},C),{ref:S})):r.createElement(void 0===k?"div":k,(0,ty.Cl)({},C,{className:m,ref:S}),u))});tP.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tP.classNames={fullWidth:tk,zeroRight:tw};var tR=function(e){var t=e.sideCar,n=(0,ty.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw Error("Sidecar medium not found");return r.createElement(i,(0,ty.Cl)({},n))};tR.isSideCarExport=!0;var tz=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,r;(a=t).styleSheet?a.styleSheet.cssText=i:a.appendChild(document.createTextNode(i)),r=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(r)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tD=function(){var e=tz();return function(t,n){r.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tT=function(){var e=tD();return function(t){return e(t.styles,t.dynamic),null}},tL={left:0,top:0,right:0,gap:0},tM=function(e){return parseInt(e||"",10)||0},t_=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],i=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[tM(n),tM(i),tM(a)]},tF=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tL;var t=t_(e),n=document.documentElement.clientWidth,i=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,i-n+t[2]-t[0])}},tN=tT(),tI="data-scroll-locked",tq=function(e,t,n,i){var a=e.left,o=e.top,r=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(i,";\n   padding-right: ").concat(l,"px ").concat(i,";\n  }\n  body[").concat(tI,"] {\n    overflow: hidden ").concat(i,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(i,";"),"margin"===n&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(r,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(i,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(i,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tw," {\n    right: ").concat(l,"px ").concat(i,";\n  }\n  \n  .").concat(tk," {\n    margin-right: ").concat(l,"px ").concat(i,";\n  }\n  \n  .").concat(tw," .").concat(tw," {\n    right: 0 ").concat(i,";\n  }\n  \n  .").concat(tk," .").concat(tk," {\n    margin-right: 0 ").concat(i,";\n  }\n  \n  body[").concat(tI,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},tH=function(){var e=parseInt(document.body.getAttribute(tI)||"0",10);return isFinite(e)?e:0},tB=function(){r.useEffect(function(){return document.body.setAttribute(tI,(tH()+1).toString()),function(){var e=tH()-1;e<=0?document.body.removeAttribute(tI):document.body.setAttribute(tI,e.toString())}},[])},tW=function(e){var t=e.noRelative,n=e.noImportant,i=e.gapMode,a=void 0===i?"margin":i;tB();var o=r.useMemo(function(){return tF(a)},[a]);return r.createElement(tN,{styles:tq(o,!t,a,n?"":"!important")})},tV=!1;if("undefined"!=typeof window)try{var tG=Object.defineProperty({},"passive",{get:function(){return tV=!0,!0}});window.addEventListener("test",tG,tG),window.removeEventListener("test",tG,tG)}catch(e){tV=!1}var tU=!!tV&&{passive:!1},tK=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},tX=function(e,t){var n=t.ownerDocument,i=t;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),tY(e,i)){var a=t$(e,i);if(a[1]>a[2])return!0}i=i.parentNode}while(i&&i!==n.body);return!1},tY=function(e,t){return"v"===e?tK(t,"overflowY"):tK(t,"overflowX")},t$=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},tZ=function(e,t,n,i,a){var o,r=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),l=r*i,c=n.target,p=t.contains(c),s=!1,d=l>0,u=0,m=0;do{if(!c)break;var f=t$(e,c),v=f[0],x=f[1]-f[2]-r*v;(v||x)&&tY(e,c)&&(u+=x,m+=v);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!p&&c!==document.body||p&&(t.contains(c)||t===c));return d&&(a&&1>Math.abs(u)||!a&&l>u)?s=!0:!d&&(a&&1>Math.abs(m)||!a&&-l>m)&&(s=!0),s},tJ=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},tQ=function(e){return[e.deltaX,e.deltaY]},t0=function(e){return e&&"current"in e?e.current:e},t1=0,t2=[];let t3=(i=function(e){var t=r.useRef([]),n=r.useRef([0,0]),i=r.useRef(),a=r.useState(t1++)[0],o=r.useState(tT)[0],l=r.useRef(e);r.useEffect(function(){l.current=e},[e]),r.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,ty.fX)([e.lockRef.current],(e.shards||[]).map(t0),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=r.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var a,o=tJ(e),r=n.current,c="deltaX"in e?e.deltaX:r[0]-o[0],p="deltaY"in e?e.deltaY:r[1]-o[1],s=e.target,d=Math.abs(c)>Math.abs(p)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var u=tX(d,s);if(!u)return!0;if(u?a=d:(a="v"===d?"h":"v",u=tX(d,s)),!u)return!1;if(!i.current&&"changedTouches"in e&&(c||p)&&(i.current=a),!a)return!0;var m=i.current||a;return tZ(m,t,e,"h"===m?c:p,!0)},[]),p=r.useCallback(function(e){if(t2.length&&t2[t2.length-1]===o){var n="deltaY"in e?tQ(e):tJ(e),i=t.current.filter(function(t){var i;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(i=t.delta,i[0]===n[0]&&i[1]===n[1])})[0];if(i&&i.should){e.cancelable&&e.preventDefault();return}if(!i){var a=(l.current.shards||[]).map(t0).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?c(e,a[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=r.useCallback(function(e,n,i,a){var o={name:e,delta:n,target:i,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(i)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=r.useCallback(function(e){n.current=tJ(e),i.current=void 0},[]),u=r.useCallback(function(t){s(t.type,tQ(t),t.target,c(t,e.lockRef.current))},[]),m=r.useCallback(function(t){s(t.type,tJ(t),t.target,c(t,e.lockRef.current))},[]);r.useEffect(function(){return t2.push(o),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:m}),document.addEventListener("wheel",p,tU),document.addEventListener("touchmove",p,tU),document.addEventListener("touchstart",d,tU),function(){t2=t2.filter(function(e){return e!==o}),document.removeEventListener("wheel",p,tU),document.removeEventListener("touchmove",p,tU),document.removeEventListener("touchstart",d,tU)}},[]);var f=e.removeScrollBar,v=e.inert;return r.createElement(r.Fragment,null,v?r.createElement(o,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,f?r.createElement(tW,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tO.useMedium(i),tR);var t4=r.forwardRef(function(e,t){return r.createElement(tP,(0,ty.Cl)({},e,{ref:t,sideCar:t3}))});t4.classNames=tP.classNames;var t6=[" ","Enter","ArrowUp","ArrowDown"],t5=[" ","Enter"],t8="Select",[t9,t7,ne]=(0,s.N)(t8),[nt,nn]=(0,u.A)(t8,[ne,e2]),ni=e2(),[na,no]=nt(t8),[nr,nl]=nt(t8),nc=e=>{let{__scopeSelect:t,children:n,open:i,defaultOpen:a,onOpenChange:o,value:l,defaultValue:c,onValueChange:p,dir:s,name:d,autoComplete:u,disabled:f,required:v,form:g}=e,h=ni(t),[b,y]=r.useState(null),[w,k]=r.useState(null),[j,E]=r.useState(!1),S=(0,m.jH)(s),[C,O]=(0,ts.i)({prop:i,defaultProp:null!=a&&a,onChange:o,caller:t8}),[A,P]=(0,ts.i)({prop:l,defaultProp:c,onChange:p,caller:t8}),R=r.useRef(null),z=!b||g||!!b.closest("form"),[D,L]=r.useState(new Set),M=Array.from(D).map(e=>e.props.value).join(";");return(0,x.jsx)(e6,{...h,children:(0,x.jsxs)(na,{required:v,scope:t,trigger:b,onTriggerChange:y,valueNode:w,onValueNodeChange:k,valueNodeHasChildren:j,onValueNodeHasChildrenChange:E,contentId:(0,T.B)(),value:A,onValueChange:P,open:C,onOpenChange:O,dir:S,triggerPointerDownPosRef:R,disabled:f,children:[(0,x.jsx)(t9.Provider,{scope:t,children:(0,x.jsx)(nr,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{L(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{L(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),z?(0,x.jsxs)(nG,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:u,value:A,onChange:e=>P(e.target.value),disabled:f,form:g,children:[void 0===A?(0,x.jsx)("option",{value:""}):null,Array.from(D)]},M):null]})})};nc.displayName=t8;var np="SelectTrigger",ns=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:i=!1,...a}=e,o=ni(n),l=no(np,n),c=l.disabled||i,s=(0,d.s)(t,l.onTriggerChange),u=t7(n),m=r.useRef("touch"),[v,g,h]=nK(e=>{let t=u().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),i=nX(t,e,n);void 0!==i&&l.onValueChange(i.value)}),b=e=>{c||(l.onOpenChange(!0),h()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,x.jsx)(e8,{asChild:!0,...o,children:(0,x.jsx)(f.sG.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":nU(l.value)?"":void 0,...a,ref:s,onClick:(0,p.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&b(e)}),onPointerDown:(0,p.m)(a.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,p.m)(a.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&t6.includes(e.key)&&(b(),e.preventDefault())})})})});ns.displayName=np;var nd="SelectValue",nu=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,children:o,placeholder:r="",...l}=e,c=no(nd,n),{onValueNodeHasChildrenChange:p}=c,s=void 0!==o,u=(0,d.s)(t,c.onValueNodeChange);return(0,eQ.N)(()=>{p(s)},[p,s]),(0,x.jsx)(f.sG.span,{...l,ref:u,style:{pointerEvents:"none"},children:nU(c.value)?(0,x.jsx)(x.Fragment,{children:r}):o})});nu.displayName=nd;var nm=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:i,...a}=e;return(0,x.jsx)(f.sG.span,{"aria-hidden":!0,...a,ref:t,children:i||"▼"})});nm.displayName="SelectIcon";var nf=e=>(0,x.jsx)(tc,{asChild:!0,...e});nf.displayName="SelectPortal";var nv="SelectContent",nx=r.forwardRef((e,t)=>{let n=no(nv,e.__scopeSelect),[i,a]=r.useState();return((0,eQ.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,x.jsx)(ny,{...e,ref:t}):i?l.createPortal((0,x.jsx)(ng,{scope:e.__scopeSelect,children:(0,x.jsx)(t9.Slot,{scope:e.__scopeSelect,children:(0,x.jsx)("div",{children:e.children})})}),i):null});nx.displayName=nv;var[ng,nh]=nt(nv),nb=(0,tp.TL)("SelectContent.RemoveScroll"),ny=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:i="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:o,onPointerDownOutside:l,side:c,sideOffset:s,align:u,alignOffset:m,arrowPadding:f,collisionBoundary:v,collisionPadding:g,sticky:h,hideWhenDetached:y,avoidCollisions:w,...E}=e,S=no(nv,n),[C,A]=r.useState(null),[P,R]=r.useState(null),z=(0,d.s)(t,e=>A(e)),[D,T]=r.useState(null),[L,M]=r.useState(null),_=t7(n),[F,N]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(C)return tb(C)},[C]),r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:j()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:j()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]);let q=r.useCallback(e=>{let[t,...n]=_().map(e=>e.ref.current),[i]=n.slice(-1),a=document.activeElement;for(let n of e)if(n===a||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===i&&P&&(P.scrollTop=P.scrollHeight),null==n||n.focus(),document.activeElement!==a))return},[_,P]),H=r.useCallback(()=>q([D,C]),[q,D,C]);r.useEffect(()=>{F&&H()},[F,H]);let{onOpenChange:B,triggerPointerDownPosRef:W}=S;r.useEffect(()=>{if(C){let e={x:0,y:0},t=t=>{var n,i,a,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(a=null==(n=W.current)?void 0:n.x)?a:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(i=W.current)?void 0:i.y)?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():C.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[C,B,W]),r.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[V,G]=nK(e=>{let t=_().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),i=nX(t,e,n);i&&setTimeout(()=>i.ref.current.focus())}),U=r.useCallback((e,t,n)=>{let i=!I.current&&!n;(void 0!==S.value&&S.value===t||i)&&(T(e),i&&(I.current=!0))},[S.value]),K=r.useCallback(()=>null==C?void 0:C.focus(),[C]),X=r.useCallback((e,t,n)=>{let i=!I.current&&!n;(void 0!==S.value&&S.value===t||i)&&M(e)},[S.value]),Y="popper"===i?nk:nw,$=Y===nk?{side:c,sideOffset:s,align:u,alignOffset:m,arrowPadding:f,collisionBoundary:v,collisionPadding:g,sticky:h,hideWhenDetached:y,avoidCollisions:w}:{};return(0,x.jsx)(ng,{scope:n,content:C,viewport:P,onViewportChange:R,itemRefCallback:U,selectedItem:D,onItemLeave:K,itemTextRefCallback:X,focusSelectedItem:H,selectedItemText:L,position:i,isPositioned:F,searchRef:V,children:(0,x.jsx)(t4,{as:nb,allowPinchZoom:!0,children:(0,x.jsx)(O,{asChild:!0,trapped:S.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,p.m)(a,e=>{var t;null==(t=S.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,x.jsx)(b,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>S.onOpenChange(!1),children:(0,x.jsx)(Y,{role:"listbox",id:S.contentId,"data-state":S.open?"open":"closed",dir:S.dir,onContextMenu:e=>e.preventDefault(),...E,...$,onPlaced:()=>N(!0),ref:z,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,p.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=_().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,i=t.indexOf(n);t=t.slice(i+1)}setTimeout(()=>q(t)),e.preventDefault()}})})})})})})});ny.displayName="SelectContentImpl";var nw=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:i,...a}=e,o=no(nv,n),l=nh(nv,n),[p,s]=r.useState(null),[u,m]=r.useState(null),v=(0,d.s)(t,e=>m(e)),g=t7(n),h=r.useRef(!1),b=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:k,focusSelectedItem:j}=l,E=r.useCallback(()=>{if(o.trigger&&o.valueNode&&p&&u&&y&&w&&k){let e=o.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),n=o.valueNode.getBoundingClientRect(),a=k.getBoundingClientRect();if("rtl"!==o.dir){let i=a.left-t.left,o=n.left-i,r=e.left-o,l=e.width+r,s=Math.max(l,t.width),d=c(o,[10,Math.max(10,window.innerWidth-10-s)]);p.style.minWidth=l+"px",p.style.left=d+"px"}else{let i=t.right-a.right,o=window.innerWidth-n.right-i,r=window.innerWidth-e.right-o,l=e.width+r,s=Math.max(l,t.width),d=c(o,[10,Math.max(10,window.innerWidth-10-s)]);p.style.minWidth=l+"px",p.style.right=d+"px"}let r=g(),l=window.innerHeight-20,s=y.scrollHeight,d=window.getComputedStyle(u),m=parseInt(d.borderTopWidth,10),f=parseInt(d.paddingTop,10),v=parseInt(d.borderBottomWidth,10),x=m+f+s+parseInt(d.paddingBottom,10)+v,b=Math.min(5*w.offsetHeight,x),j=window.getComputedStyle(y),E=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),C=e.top+e.height/2-10,O=w.offsetHeight/2,A=m+f+(w.offsetTop+O);if(A<=C){let e=r.length>0&&w===r[r.length-1].ref.current;p.style.bottom="0px";let t=Math.max(l-C,O+(e?S:0)+(u.clientHeight-y.offsetTop-y.offsetHeight)+v);p.style.height=A+t+"px"}else{let e=r.length>0&&w===r[0].ref.current;p.style.top="0px";let t=Math.max(C,m+y.offsetTop+(e?E:0)+O);p.style.height=t+(x-A)+"px",y.scrollTop=A-C+y.offsetTop}p.style.margin="".concat(10,"px 0"),p.style.minHeight=b+"px",p.style.maxHeight=l+"px",null==i||i(),requestAnimationFrame(()=>h.current=!0)}},[g,o.trigger,o.valueNode,p,u,y,w,k,o.dir,i]);(0,eQ.N)(()=>E(),[E]);let[S,C]=r.useState();(0,eQ.N)(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);let O=r.useCallback(e=>{e&&!0===b.current&&(E(),null==j||j(),b.current=!1)},[E,j]);return(0,x.jsx)(nj,{scope:n,contentWrapper:p,shouldExpandOnScrollRef:h,onScrollButtonChange:O,children:(0,x.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,x.jsx)(f.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});nw.displayName="SelectItemAlignedPosition";var nk=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:i="start",collisionPadding:a=10,...o}=e,r=ni(n);return(0,x.jsx)(tt,{...r,...o,ref:t,align:i,collisionPadding:a,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nk.displayName="SelectPopperPosition";var[nj,nE]=nt(nv,{}),nS="SelectViewport",nC=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:i,...a}=e,o=nh(nS,n),l=nE(nS,n),c=(0,d.s)(t,o.onViewportChange),s=r.useRef(0);return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,x.jsx)(t9.Slot,{scope:n,children:(0,x.jsx)(f.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,p.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:i}=l;if((null==i?void 0:i.current)&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let i=window.innerHeight-20,a=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(a<i){let o=a+e,r=Math.min(i,o),l=o-r;n.style.height=r+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});nC.displayName=nS;var nO="SelectGroup",[nA,nP]=nt(nO);r.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e,a=(0,T.B)();return(0,x.jsx)(nA,{scope:n,id:a,children:(0,x.jsx)(f.sG.div,{role:"group","aria-labelledby":a,...i,ref:t})})}).displayName=nO;var nR="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e,a=nP(nR,n);return(0,x.jsx)(f.sG.div,{id:a.id,...i,ref:t})}).displayName=nR;var nz="SelectItem",[nD,nT]=nt(nz),nL=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:i,disabled:a=!1,textValue:o,...l}=e,c=no(nz,n),s=nh(nz,n),u=c.value===i,[m,v]=r.useState(null!=o?o:""),[g,h]=r.useState(!1),b=(0,d.s)(t,e=>{var t;return null==(t=s.itemRefCallback)?void 0:t.call(s,e,i,a)}),y=(0,T.B)(),w=r.useRef("touch"),k=()=>{a||(c.onValueChange(i),c.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,x.jsx)(nD,{scope:n,value:i,disabled:a,textId:y,isSelected:u,onItemTextChange:r.useCallback(e=>{v(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,x.jsx)(t9.ItemSlot,{scope:n,value:i,disabled:a,textValue:m,children:(0,x.jsx)(f.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":g?"":void 0,"aria-selected":u&&g,"data-state":u?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...l,ref:b,onFocus:(0,p.m)(l.onFocus,()=>h(!0)),onBlur:(0,p.m)(l.onBlur,()=>h(!1)),onClick:(0,p.m)(l.onClick,()=>{"mouse"!==w.current&&k()}),onPointerUp:(0,p.m)(l.onPointerUp,()=>{"mouse"===w.current&&k()}),onPointerDown:(0,p.m)(l.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,p.m)(l.onPointerMove,e=>{if(w.current=e.pointerType,a){var t;null==(t=s.onItemLeave)||t.call(s)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,p.m)(l.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=s.onItemLeave)||t.call(s)}}),onKeyDown:(0,p.m)(l.onKeyDown,e=>{var t;((null==(t=s.searchRef)?void 0:t.current)===""||" "!==e.key)&&(t5.includes(e.key)&&k()," "===e.key&&e.preventDefault())})})})})});nL.displayName=nz;var nM="SelectItemText",n_=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...o}=e,c=no(nM,n),p=nh(nM,n),s=nT(nM,n),u=nl(nM,n),[m,v]=r.useState(null),g=(0,d.s)(t,e=>v(e),s.onItemTextChange,e=>{var t;return null==(t=p.itemTextRefCallback)?void 0:t.call(p,e,s.value,s.disabled)}),h=null==m?void 0:m.textContent,b=r.useMemo(()=>(0,x.jsx)("option",{value:s.value,disabled:s.disabled,children:h},s.value),[s.disabled,s.value,h]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=u;return(0,eQ.N)(()=>(y(b),()=>w(b)),[y,w,b]),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(f.sG.span,{id:s.textId,...o,ref:g}),s.isSelected&&c.valueNode&&!c.valueNodeHasChildren?l.createPortal(o.children,c.valueNode):null]})});n_.displayName=nM;var nF="SelectItemIndicator",nN=r.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e;return nT(nF,n).isSelected?(0,x.jsx)(f.sG.span,{"aria-hidden":!0,...i,ref:t}):null});nN.displayName=nF;var nI="SelectScrollUpButton",nq=r.forwardRef((e,t)=>{let n=nh(nI,e.__scopeSelect),i=nE(nI,e.__scopeSelect),[a,o]=r.useState(!1),l=(0,d.s)(t,i.onScrollButtonChange);return(0,eQ.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){o(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),a?(0,x.jsx)(nW,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nq.displayName=nI;var nH="SelectScrollDownButton",nB=r.forwardRef((e,t)=>{let n=nh(nH,e.__scopeSelect),i=nE(nH,e.__scopeSelect),[a,o]=r.useState(!1),l=(0,d.s)(t,i.onScrollButtonChange);return(0,eQ.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),a?(0,x.jsx)(nW,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nB.displayName=nH;var nW=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:i,...a}=e,o=nh("SelectScrollButton",n),l=r.useRef(null),c=t7(n),s=r.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,eQ.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[c]),(0,x.jsx)(f.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,p.m)(a.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(i,50))}),onPointerMove:(0,p.m)(a.onPointerMove,()=>{var e;null==(e=o.onItemLeave)||e.call(o),null===l.current&&(l.current=window.setInterval(i,50))}),onPointerLeave:(0,p.m)(a.onPointerLeave,()=>{s()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e;return(0,x.jsx)(f.sG.div,{"aria-hidden":!0,...i,ref:t})}).displayName="SelectSeparator";var nV="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...i}=e,a=ni(n),o=no(nV,n),r=nh(nV,n);return o.open&&"popper"===r.position?(0,x.jsx)(ta,{...a,...i,ref:t}):null}).displayName=nV;var nG=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:i,...a}=e,o=r.useRef(null),l=(0,d.s)(t,o),c=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(i);return r.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==i&&t){let n=new Event("change",{bubbles:!0});t.call(e,i),e.dispatchEvent(n)}},[c,i]),(0,x.jsx)(f.sG.select,{...a,style:{...td,...a.style},ref:l,defaultValue:i})});function nU(e){return""===e||void 0===e}function nK(e){let t=(0,v.c)(e),n=r.useRef(""),i=r.useRef(0),a=r.useCallback(e=>{let a=n.current+e;t(a),function e(t){n.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(a)},[t]),o=r.useCallback(()=>{n.current="",window.clearTimeout(i.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(i.current),[]),[n,a,o]}function nX(e,t,n){var i,a;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,r=n?e.indexOf(n):-1,l=(i=e,a=Math.max(r,0),i.map((e,t)=>i[(a+t)%i.length]));1===o.length&&(l=l.filter(e=>e!==n));let c=l.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return c!==n?c:void 0}nG.displayName="SelectBubbleInput";var nY=nc,n$=ns,nZ=nu,nJ=nm,nQ=nf,n0=nx,n1=nC,n2=nL,n3=n_,n4=nN,n6=nq,n5=nB},901:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return i}});let i=n(8229)._(n(2115)).default.createContext(null)},1046:(e,t,n)=>{"use strict";n.d(t,{VB:()=>Q});var i=n(2115),a=n(8637),o=n(9249);let r=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function l(e,t,n){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let n=t.split(".").pop().toLowerCase(),i=r.get(n);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:a}=e,o="string"==typeof t?t:"string"==typeof a&&a.length>0?a:`./${e.name}`;return"string"!=typeof i.path&&c(i,"path",o),void 0!==n&&Object.defineProperty(i,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),c(i,"relativePath",o),i}function c(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}let p=[".DS_Store","Thumbs.db"];function s(e){return"object"==typeof e&&null!==e}function d(e){return e.filter(e=>-1===p.indexOf(e.name))}function u(e){if(null===e)return[];let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i)}return t}function m(e){if("function"!=typeof e.webkitGetAsEntry)return f(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?x(t):f(e,t)}function f(e,t){return(0,o.sH)(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,l(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return l(i,null!=(n=null==t?void 0:t.fullPath)?n:void 0)})}function v(e){return(0,o.sH)(this,void 0,void 0,function*(){return e.isDirectory?x(e):function(e){return(0,o.sH)(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(n=>{t(l(n,e.fullPath))},e=>{n(e)})})})}(e)})}function x(e){let t=e.createReader();return new Promise((e,n)=>{let i=[];!function a(){t.readEntries(t=>(0,o.sH)(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(v));i.push(e),a()}else try{let t=yield Promise.all(i);e(t)}catch(e){n(e)}}),e=>{n(e)})}()})}var g=n(2462);function h(e){return function(e){if(Array.isArray(e))return E(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||j(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach(function(t){w(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var o=[],r=!0,l=!1;try{for(a=a.call(e);!(r=(n=a.next()).done)&&(o.push(n.value),!t||o.length!==t);r=!0);}catch(e){l=!0,i=e}finally{try{r||null==a.return||a.return()}finally{if(l)throw i}}return o}}(e,t)||j(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,t){if(e){if("string"==typeof e)return E(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return E(e,t)}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}var S="function"==typeof g?g:g.default,C=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),n=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(n)}},O=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},A=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},P={code:"too-many-files",message:"Too many files"};function R(e,t){var n="application/x-moz-file"===e.type||S(e,t);return[n,n?null:C(t)]}function z(e,t,n){if(D(e.size)){if(D(t)&&D(n)){if(e.size>n)return[!1,O(n)];if(e.size<t)return[!1,A(t)]}else if(D(t)&&e.size<t)return[!1,A(t)];else if(D(n)&&e.size>n)return[!1,O(n)]}return[!0,null]}function D(e){return null!=e}function T(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function L(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function M(e){e.preventDefault()}function _(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,i=Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.some(function(t){return!T(e)&&t&&t.apply(void 0,[e].concat(i)),T(e)})}}function F(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function N(e){return/^.*\.[\w]+$/.test(e)}var I=["children"],q=["open"],H=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],B=["refKey","onChange","onClick"];function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var o=[],r=!0,l=!1;try{for(a=a.call(e);!(r=(n=a.next()).done)&&(o.push(n.value),!t||o.length!==t);r=!0);}catch(e){l=!0,i=e}finally{try{r||null==a.return||a.return()}finally{if(l)throw i}}return o}}(e,t)||V(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){if(e){if("string"==typeof e)return G(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return G(e,t)}}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function U(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function K(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?U(Object(n),!0).forEach(function(t){X(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):U(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function X(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Y(e,t){if(null==e)return{};var n,i,a=function(e,t){if(null==e)return{};var n,i,a={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var $=(0,i.forwardRef)(function(e,t){var n=e.children,a=Q(Y(e,I)),o=a.open,r=Y(a,q);return(0,i.useImperativeHandle)(t,function(){return{open:o}},[o]),i.createElement(i.Fragment,null,n(K(K({},r),{},{open:o})))});$.displayName="Dropzone";var Z={disabled:!1,getFilesFromEvent:function(e){return(0,o.sH)(this,void 0,void 0,function*(){var t;if(s(e)&&s(e.dataTransfer))return function(e,t){return(0,o.sH)(this,void 0,void 0,function*(){if(e.items){let n=u(e.items).filter(e=>"file"===e.kind);return"drop"!==t?n:d(function e(t){return t.reduce((t,n)=>[...t,...Array.isArray(n)?e(n):[n]],[])}((yield Promise.all(n.map(m)))))}return d(u(e.files).map(e=>l(e)))})}(e.dataTransfer,e.type);if(s(t=e)&&s(t.target))return u(e.target.files).map(e=>l(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return(0,o.sH)(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>l(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};$.defaultProps=Z,$.propTypes={children:a.func,accept:a.objectOf(a.arrayOf(a.string)),multiple:a.bool,preventDropOnDocument:a.bool,noClick:a.bool,noKeyboard:a.bool,noDrag:a.bool,noDragEventsBubbling:a.bool,minSize:a.number,maxSize:a.number,maxFiles:a.number,disabled:a.bool,getFilesFromEvent:a.func,onFileDialogCancel:a.func,onFileDialogOpen:a.func,useFsAccessApi:a.bool,autoFocus:a.bool,onDragEnter:a.func,onDragLeave:a.func,onDragOver:a.func,onDrop:a.func,onDropAccepted:a.func,onDropRejected:a.func,onError:a.func,validator:a.func};var J={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=K(K({},Z),e),n=t.accept,a=t.disabled,o=t.getFilesFromEvent,r=t.maxSize,l=t.minSize,c=t.multiple,p=t.maxFiles,s=t.onDragEnter,d=t.onDragLeave,u=t.onDragOver,m=t.onDrop,f=t.onDropAccepted,v=t.onDropRejected,x=t.onFileDialogCancel,g=t.onFileDialogOpen,b=t.useFsAccessApi,j=t.autoFocus,E=t.preventDropOnDocument,S=t.noClick,C=t.noKeyboard,O=t.noDrag,A=t.noDragEventsBubbling,I=t.onError,q=t.validator,U=(0,i.useMemo)(function(){return D(n)?Object.entries(n).reduce(function(e,t){var n=k(t,2),i=n[0],a=n[1];return[].concat(h(e),[i],h(a))},[]).filter(function(e){return F(e)||N(e)}).join(","):void 0},[n]),$=(0,i.useMemo)(function(){return D(n)?[{description:"Files",accept:Object.entries(n).filter(function(e){var t=k(e,2),n=t[0],i=t[1],a=!0;return F(n)||(console.warn('Skipped "'.concat(n,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),a=!1),Array.isArray(i)&&i.every(N)||(console.warn('Skipped "'.concat(n,'" because an invalid file extension was provided.')),a=!1),a}).reduce(function(e,t){var n=k(t,2),i=n[0],a=n[1];return y(y({},e),{},w({},i,a))},{})}]:n},[n]),Q=(0,i.useMemo)(function(){return"function"==typeof g?g:et},[g]),en=(0,i.useMemo)(function(){return"function"==typeof x?x:et},[x]),ei=(0,i.useRef)(null),ea=(0,i.useRef)(null),eo=W((0,i.useReducer)(ee,J),2),er=eo[0],el=eo[1],ec=er.isFocused,ep=er.isFileDialogActive,es=(0,i.useRef)("undefined"!=typeof window&&window.isSecureContext&&b&&"showOpenFilePicker"in window),ed=function(){!es.current&&ep&&setTimeout(function(){ea.current&&(ea.current.files.length||(el({type:"closeDialog"}),en()))},300)};(0,i.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[ea,ep,en,es]);var eu=(0,i.useRef)([]),em=function(e){ei.current&&ei.current.contains(e.target)||(e.preventDefault(),eu.current=[])};(0,i.useEffect)(function(){return E&&(document.addEventListener("dragover",M,!1),document.addEventListener("drop",em,!1)),function(){E&&(document.removeEventListener("dragover",M),document.removeEventListener("drop",em))}},[ei,E]),(0,i.useEffect)(function(){return!a&&j&&ei.current&&ei.current.focus(),function(){}},[ei,j,a]);var ef=(0,i.useCallback)(function(e){I?I(e):console.error(e)},[I]),ev=(0,i.useCallback)(function(e){var t;e.preventDefault(),e.persist(),eA(e),eu.current=[].concat(function(e){if(Array.isArray(e))return G(e)}(t=eu.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||V(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),L(e)&&Promise.resolve(o(e)).then(function(t){if(!T(e)||A){var n,i,a,o,d,u,m,f,v=t.length,x=v>0&&(i=(n={files:t,accept:U,minSize:l,maxSize:r,multiple:c,maxFiles:p,validator:q}).files,a=n.accept,o=n.minSize,d=n.maxSize,u=n.multiple,m=n.maxFiles,f=n.validator,(!!u||!(i.length>1))&&(!u||!(m>=1)||!(i.length>m))&&i.every(function(e){var t=k(R(e,a),1)[0],n=k(z(e,o,d),1)[0],i=f?f(e):null;return t&&n&&!i}));el({isDragAccept:x,isDragReject:v>0&&!x,isDragActive:!0,type:"setDraggedFiles"}),s&&s(e)}}).catch(function(e){return ef(e)})},[o,s,ef,A,U,l,r,c,p,q]),ex=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eA(e);var t=L(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&u&&u(e),!1},[u,A]),eg=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eA(e);var t=eu.current.filter(function(e){return ei.current&&ei.current.contains(e)}),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),eu.current=t,!(t.length>0)&&(el({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),L(e)&&d&&d(e))},[ei,d,A]),eh=(0,i.useCallback)(function(e,t){var n=[],i=[];e.forEach(function(e){var t=W(R(e,U),2),a=t[0],o=t[1],c=W(z(e,l,r),2),p=c[0],s=c[1],d=q?q(e):null;if(a&&p&&!d)n.push(e);else{var u=[o,s];d&&(u=u.concat(d)),i.push({file:e,errors:u.filter(function(e){return e})})}}),(!c&&n.length>1||c&&p>=1&&n.length>p)&&(n.forEach(function(e){i.push({file:e,errors:[P]})}),n.splice(0)),el({acceptedFiles:n,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),m&&m(n,i,t),i.length>0&&v&&v(i,t),n.length>0&&f&&f(n,t)},[el,c,U,l,r,p,m,f,v,q]),eb=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eA(e),eu.current=[],L(e)&&Promise.resolve(o(e)).then(function(t){(!T(e)||A)&&eh(t,e)}).catch(function(e){return ef(e)}),el({type:"reset"})},[o,eh,ef,A]),ey=(0,i.useCallback)(function(){if(es.current){el({type:"openDialog"}),Q(),window.showOpenFilePicker({multiple:c,types:$}).then(function(e){return o(e)}).then(function(e){eh(e,null),el({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(en(e),el({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(es.current=!1,ea.current?(ea.current.value=null,ea.current.click()):ef(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ef(e)});return}ea.current&&(el({type:"openDialog"}),Q(),ea.current.value=null,ea.current.click())},[el,Q,en,b,eh,ef,$,c]),ew=(0,i.useCallback)(function(e){ei.current&&ei.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ey())},[ei,ey]),ek=(0,i.useCallback)(function(){el({type:"focus"})},[]),ej=(0,i.useCallback)(function(){el({type:"blur"})},[]),eE=(0,i.useCallback)(function(){S||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ey,0):ey())},[S,ey]),eS=function(e){return a?null:e},eC=function(e){return C?null:eS(e)},eO=function(e){return O?null:eS(e)},eA=function(e){A&&e.stopPropagation()},eP=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.role,i=e.onKeyDown,o=e.onFocus,r=e.onBlur,l=e.onClick,c=e.onDragEnter,p=e.onDragOver,s=e.onDragLeave,d=e.onDrop,u=Y(e,H);return K(K(X({onKeyDown:eC(_(i,ew)),onFocus:eC(_(o,ek)),onBlur:eC(_(r,ej)),onClick:eS(_(l,eE)),onDragEnter:eO(_(c,ev)),onDragOver:eO(_(p,ex)),onDragLeave:eO(_(s,eg)),onDrop:eO(_(d,eb)),role:"string"==typeof n&&""!==n?n:"presentation"},void 0===t?"ref":t,ei),a||C?{}:{tabIndex:0}),u)}},[ei,ew,ek,ej,eE,ev,ex,eg,eb,C,O,a]),eR=(0,i.useCallback)(function(e){e.stopPropagation()},[]),ez=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.onChange,i=e.onClick,a=Y(e,B);return K(K({},X({accept:U,multiple:c,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eS(_(n,eb)),onClick:eS(_(i,eR)),tabIndex:-1},void 0===t?"ref":t,ea)),a)}},[ea,n,c,eb,a]);return K(K({},er),{},{isFocused:ec&&!a,getRootProps:eP,getInputProps:ez,rootRef:ei,inputRef:ea,open:eS(ey)})}function ee(e,t){switch(t.type){case"focus":return K(K({},e),{},{isFocused:!0});case"blur":return K(K({},e),{},{isFocused:!1});case"openDialog":return K(K({},J),{},{isFileDialogActive:!0});case"closeDialog":return K(K({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return K(K({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return K(K({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return K({},J);default:return e}}function et(){}},1193:(e,t)=>{"use strict";function n(e){var t;let{config:n,src:i,width:a,quality:o}=e,r=o||(null==(t=n.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return n.path+"?url="+encodeURIComponent(i)+"&w="+a+"&q="+r+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),n.__next_img_default=!0;let i=n},1469:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return c},getImageProps:function(){return l}});let i=n(8229),a=n(8883),o=n(3063),r=i._(n(1193));function l(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let c=o.Image},2085:(e,t,n)=>{"use strict";n.d(t,{F:()=>r});var i=n(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=i.$,r=(e,t)=>n=>{var i;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:r,defaultVariants:l}=t,c=Object.keys(r).map(e=>{let t=null==n?void 0:n[e],i=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(i);return r[e][o]}),p=n&&Object.entries(n).reduce((e,t)=>{let[n,i]=t;return void 0===i||(e[n]=i),e},{});return o(e,c,null==t||null==(i=t.compoundVariants)?void 0:i.reduce((e,t)=>{let{class:n,className:i,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...p}[t]):({...l,...p})[t]===n})?[...e,n,i]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2462:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(0===n.length)return!0;var i=e.name||"",a=(e.type||"").toLowerCase(),o=a.replace(/\/.*$/,"");return n.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):a===t})}return!0}},2464:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return i}});let i=n(8229)._(n(2115)).default.createContext({})},2948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3063:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let i=n(8229),a=n(6966),o=n(5155),r=a._(n(2115)),l=i._(n(7650)),c=i._(n(5564)),p=n(8883),s=n(5840),d=n(6752);n(3230);let u=n(901),m=i._(n(1193)),f=n(6654),v={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,n,i,a,o,r){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,a=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function g(e){return r.use?{fetchPriority:e}:{fetchpriority:e}}let h=(0,r.forwardRef)((e,t)=>{let{src:n,srcSet:i,sizes:a,height:l,width:c,decoding:p,className:s,style:d,fetchPriority:u,placeholder:m,loading:v,unoptimized:h,fill:b,onLoadRef:y,onLoadingCompleteRef:w,setBlurComplete:k,setShowAltText:j,sizesInput:E,onLoad:S,onError:C,...O}=e,A=(0,r.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&x(e,m,y,w,k,h,E))},[n,m,y,w,k,C,h,E]),P=(0,f.useMergedRef)(t,A);return(0,o.jsx)("img",{...O,...g(u),loading:v,width:c,height:l,decoding:p,"data-nimg":b?"fill":"1",className:s,style:d,sizes:a,srcSet:i,src:n,ref:P,onLoad:e=>{x(e.currentTarget,m,y,w,k,h,E)},onError:e=>{j(!0),"empty"!==m&&k(!0),C&&C(e)}})});function b(e){let{isAppRouter:t,imgAttributes:n}=e,i={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...g(n.fetchPriority)};return t&&l.default.preload?(l.default.preload(n.src,i),null):(0,o.jsx)(c.default,{children:(0,o.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...i},"__nimg-"+n.src+n.srcSet+n.sizes)})}let y=(0,r.forwardRef)((e,t)=>{let n=(0,r.useContext)(u.RouterContext),i=(0,r.useContext)(d.ImageConfigContext),a=(0,r.useMemo)(()=>{var e;let t=v||i||s.imageConfigDefault,n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),a=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:a,qualities:o}},[i]),{onLoad:l,onLoadingComplete:c}=e,f=(0,r.useRef)(l);(0,r.useEffect)(()=>{f.current=l},[l]);let x=(0,r.useRef)(c);(0,r.useEffect)(()=>{x.current=c},[c]);let[g,y]=(0,r.useState)(!1),[w,k]=(0,r.useState)(!1),{props:j,meta:E}=(0,p.getImgProps)(e,{defaultLoader:m.default,imgConf:a,blurComplete:g,showAltText:w});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(h,{...j,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:f,onLoadingCompleteRef:x,setBlurComplete:y,setShowAltText:k,sizesInput:e.sizes,ref:t}),E.priority?(0,o.jsx)(b,{isAppRouter:!n,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4229:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5029:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}});let i=n(2115),a=i.useLayoutEffect,o=i.useEffect;function r(e){let{headManager:t,reduceComponentsToState:n}=e;function r(){if(t&&t.mountedInstances){let a=i.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(a,e))}}return a(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),a(()=>(t&&(t._pendingUpdate=r),()=>{t&&(t._pendingUpdate=r)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{"use strict";function n(e){let{widthInt:t,heightInt:n,blurWidth:i,blurHeight:a,blurDataURL:o,objectFit:r}=e,l=i?40*i:t,c=a?40*a:n,p=l&&c?"viewBox='0 0 "+l+" "+c+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+p+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(p?"none":"contain"===r?"xMidYMid":"cover"===r?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},5196:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5339:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5564:(e,t,n)=>{"use strict";var i=n(9509);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return x},defaultHead:function(){return u}});let a=n(8229),o=n(6966),r=n(5155),l=o._(n(2115)),c=a._(n(5029)),p=n(2464),s=n(2830),d=n(7544);function u(e){void 0===e&&(e=!1);let t=[(0,r.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,r.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(3230);let f=["name","httpEquiv","charSet","itemProp"];function v(e,t){let{inAmpMode:n}=t;return e.reduce(m,[]).reverse().concat(u(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,i={};return a=>{let o=!0,r=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){r=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?o=!1:t.add(a.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(a.props.hasOwnProperty(t))if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=a.props[t],n=i[t]||new Set;("name"!==t||!r)&&n.has(e)?o=!1:(n.add(e),i[t]=n)}}}return o}}()).reverse().map((e,t)=>{let a=e.key||t;if(i.env.__NEXT_OPTIMIZE_FONTS&&!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,l.default.cloneElement(e,t)}return l.default.cloneElement(e,{key:a})})}let x=function(e){let{children:t}=e,n=(0,l.useContext)(p.AmpStateContext),i=(0,l.useContext)(s.HeadManagerContext);return(0,r.jsx)(c.default,{reduceComponentsToState:v,headManager:i,inAmpMode:(0,d.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return i}});let n=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6474:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6752:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let i=n(8229)._(n(2115)),a=n(5840),o=i.default.createContext(a.imageConfigDefault)},6766:(e,t,n)=>{"use strict";n.d(t,{default:()=>a.a});var i=n(1469),a=n.n(i)},7434:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7544:(e,t)=>{"use strict";function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||n&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},7863:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8637:(e,t,n)=>{e.exports=n(9399)()},8883:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return c}}),n(3230);let i=n(5100),a=n(5840),o=["-moz-initial","fill","none","scale-down",void 0];function r(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function c(e,t){var n,c;let p,s,d,{src:u,sizes:m,unoptimized:f=!1,priority:v=!1,loading:x,className:g,quality:h,width:b,height:y,fill:w=!1,style:k,overrideSrc:j,onLoad:E,onLoadingComplete:S,placeholder:C="empty",blurDataURL:O,fetchPriority:A,decoding:P="async",layout:R,objectFit:z,objectPosition:D,lazyBoundary:T,lazyRoot:L,...M}=e,{imgConf:_,showAltText:F,blurComplete:N,defaultLoader:I}=t,q=_||a.imageConfigDefault;if("allSizes"in q)p=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),i=null==(n=q.qualities)?void 0:n.sort((e,t)=>e-t);p={...q,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===I)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let H=M.loader||I;delete M.loader,delete M.srcSet;let B="__next_img_default"in H;if(B){if("custom"===p.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=H;H=t=>{let{config:n,...i}=t;return e(i)}}if(R){"fill"===R&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(k={...k,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!m&&(m=t)}let W="",V=l(b),G=l(y);if((c=u)&&"object"==typeof c&&(r(c)||void 0!==c.src)){let e=r(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(s=e.blurWidth,d=e.blurHeight,O=O||e.blurDataURL,W=e.src,!w)if(V||G){if(V&&!G){let t=V/e.width;G=Math.round(e.height*t)}else if(!V&&G){let t=G/e.height;V=Math.round(e.width*t)}}else V=e.width,G=e.height}let U=!v&&("lazy"===x||void 0===x);(!(u="string"==typeof u?u:W)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,U=!1),p.unoptimized&&(f=!0),B&&!p.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(f=!0);let K=l(h),X=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:D}:{},F?{}:{color:"transparent"},k),Y=N||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:V,heightInt:G,blurWidth:s,blurHeight:d,blurDataURL:O||"",objectFit:X.objectFit})+'")':'url("'+C+'")',$=o.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=Y?{backgroundSize:$,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},J=function(e){let{config:t,src:n,unoptimized:i,width:a,quality:o,sizes:r,loader:l}=e;if(i)return{src:n,srcSet:void 0,sizes:void 0};let{widths:c,kind:p}=function(e,t,n){let{deviceSizes:i,allSizes:a}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(n);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:a,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))],kind:"x"}}(t,a,r),s=c.length-1;return{sizes:r||"w"!==p?r:"100vw",srcSet:c.map((e,i)=>l({config:t,src:n,quality:o,width:e})+" "+("w"===p?e:i+1)+p).join(", "),src:l({config:t,src:n,quality:o,width:c[s]})}}({config:p,src:u,unoptimized:f,width:V,quality:K,sizes:m,loader:H});return{props:{...M,loading:U?"lazy":x,fetchPriority:A,width:V,height:G,decoding:P,className:g,style:{...X,...Z},sizes:J.sizes,srcSet:J.srcSet,src:j||J.src},meta:{unoptimized:f,priority:v,placeholder:C,fill:w}}}},9249:(e,t,n)=>{"use strict";n.d(t,{Cl:()=>i,Tt:()=>a,fX:()=>r,sH:()=>o});var i=function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)0>t.indexOf(i[a])&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(n[i[a]]=e[i[a]]);return n}function o(e,t,n,i){return new(n||(n=Promise))(function(a,o){function r(e){try{c(i.next(e))}catch(e){o(e)}}function l(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?a(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(r,l)}c((i=i.apply(e,t||[])).next())})}Object.create;function r(e,t,n){if(n||2==arguments.length)for(var i,a=0,o=t.length;a<o;a++)!i&&a in t||(i||(i=Array.prototype.slice.call(t,0,a)),i[a]=t[a]);return e.concat(i||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},9399:(e,t,n)=>{"use strict";var i=n(2948);function a(){}function o(){}o.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,o,r){if(r!==i){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:a};return n.PropTypes=n,n}},9869:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var i=n(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),r=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var p={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,i.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:r,className:s="",children:d,iconNode:u,...m}=e;return(0,i.createElement)("svg",{ref:t,...p,width:a,height:a,stroke:n,strokeWidth:r?24*Number(o)/Number(a):o,className:l("lucide",s),...!d&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,n]=e;return(0,i.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,i.forwardRef)((n,o)=>{let{className:c,...p}=n;return(0,i.createElement)(s,{ref:o,iconNode:t,className:l("lucide-".concat(a(r(e))),"lucide-".concat(e),c),...p})});return n.displayName=r(e),n}}}]);