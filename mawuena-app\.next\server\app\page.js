"use strict";(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var a=t(9916),o=t(2507);async function n(){let e=await (0,o.U)(),{data:r,error:t}=await e.auth.getUser();t||!r?.user?(0,a.redirect)("/login"):(0,a.redirect)("/dashboard")}},1630:e=>{e.exports=require("http")},1645:e=>{e.exports=require("net")},1997:e=>{e.exports=require("punycode")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4631:e=>{e.exports=require("tls")},4735:e=>{e.exports=require("events")},5511:e=>{e.exports=require("crypto")},5591:e=>{e.exports=require("https")},7910:e=>{e.exports=require("stream")},8937:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>d});var a=t(5239),o=t(8088),n=t(8170),s=t.n(n),i=t(893),p={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);t.d(r,p);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{e.exports=require("buffer")},9551:e=>{e.exports=require("url")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,145,658,875,271],()=>t(8937));module.exports=a})();