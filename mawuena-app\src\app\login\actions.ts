'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { db } from '@/lib/database'

export async function login(formData: FormData) {
  const supabase = await createClient()

  // Validate inputs
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  if (!email || !password) {
    redirect('/login?error=missing-credentials')
  }

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    redirect(`/login?error=${encodeURIComponent(error.message)}`)
  }

  // Check if user profile exists in our database
  if (data.user) {
    try {
      await db.getUser(data.user.id)
    } catch (error) {
      // User doesn't exist in our database, create profile
      await db.createUser({
        id: data.user.id,
        email: data.user.email!,
        instagram_handle: null,
        business_name: null,
        mastra_agent_config: {},
      })
    }
  }

  revalidatePath('/', 'layout')
  redirect('/dashboard')
}

export async function signup(formData: FormData) {
  const supabase = await createClient()

  // Validate inputs
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const businessName = formData.get('business_name') as string

  if (!email || !password) {
    redirect('/login?error=missing-credentials')
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
    },
  })

  if (error) {
    redirect(`/login?error=${encodeURIComponent(error.message)}`)
  }

  // Create user profile in our database
  if (data.user) {
    try {
      await db.createUser({
        id: data.user.id,
        email: data.user.email!,
        instagram_handle: null,
        business_name: businessName || null,
        mastra_agent_config: {},
      })
    } catch (error) {
      console.error('Error creating user profile:', error)
    }
  }

  revalidatePath('/', 'layout')
  redirect('/login?message=check-email')
}

export async function signout() {
  const supabase = await createClient()
  
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    redirect('/error')
  }

  revalidatePath('/', 'layout')
  redirect('/login')
}
