import { ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { signout } from '@/app/login/actions'
import Link from 'next/link'

interface DashboardLayoutProps {
  children: ReactNode
  title: string
  description?: string
  user: {
    email: string
  }
}

export default function DashboardLayout({ children, title, description, user }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
              {description && (
                <p className="mt-1 text-sm text-gray-600">{description}</p>
              )}
            </div>
            <div className="flex items-center gap-4">
              <nav className="flex gap-4">
                <Button variant="ghost" asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </Button>
                <Button variant="ghost" asChild>
                  <Link href="/dashboard/inventory">Inventory</Link>
                </Button>
              </nav>
              <form action={signout}>
                <Button variant="outline" type="submit">
                  Sign Out
                </Button>
              </form>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-4 py-6 sm:px-0">
          {children}
        </div>
      </div>
    </div>
  )
}
