# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/inspect-js/is-date-object/compare/v1.0.5...v1.1.0) - 2024-12-12

### Commits

- [actions] reuse common workflows [`35c5af0`](https://github.com/inspect-js/is-date-object/commit/35c5af06344e8707b82f0ae24821e0a2a0cfb02d)
- [meta] use `npmignore` to autogenerate an npmignore file [`db6113c`](https://github.com/inspect-js/is-date-object/commit/db6113c3b98c9be87038d9b11a39166554f6c2b0)
- [New] add types [`4f1d9b3`](https://github.com/inspect-js/is-date-object/commit/4f1d9b3d1908d5bfd536128fe8fa49e16fd48761)
- [actions] split out node 10-20, and 20+ [`c9a1e4f`](https://github.com/inspect-js/is-date-object/commit/c9a1e4f1fd50277ef85f04ee293795e433436f8c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `core-js`, `safe-publish-latest`, `tape` [`35a2864`](https://github.com/inspect-js/is-date-object/commit/35a2864ac5913ae6d14d80398cc5910abf4c528d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`b670bca`](https://github.com/inspect-js/is-date-object/commit/b670bcafec01141b5f2ba6e3ea22bde4124d0a2e)
- [actions] update rebase action to use reusable workflow [`d6bb341`](https://github.com/inspect-js/is-date-object/commit/d6bb34105613962581bfd7509ee7cc02110b6890)
- [actions] update codecov uploader [`f850678`](https://github.com/inspect-js/is-date-object/commit/f8506786159dc9072c62c77e6609ad7db14551af)
- [Robustness] use `call-bound` [`18ed326`](https://github.com/inspect-js/is-date-object/commit/18ed326470e86ffc66ea3cd3e642f44a7036948e)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `npmignore`, `tape` [`f0e792f`](https://github.com/inspect-js/is-date-object/commit/f0e792f7b13b0f9567fbb607fc90278e52377a0f)
- [meta] add `exports` field [`342351f`](https://github.com/inspect-js/is-date-object/commit/342351f37f3ed823ea7d7da2aa32099e6eb00852)
- [Tests] replace `aud` with `npm audit` [`9b9b9cf`](https://github.com/inspect-js/is-date-object/commit/9b9b9cf665114a211b682402e10486bf2c89bdfa)
- [Deps] update `has-tostringtag` [`1bc37ab`](https://github.com/inspect-js/is-date-object/commit/1bc37ab6e8122aecc2a4283e1b3920c61c8d272e)
- [meta] add `sideEffects` flag [`86d3a16`](https://github.com/inspect-js/is-date-object/commit/86d3a16d65376ac84d98c8aa55ff7b01cfd01ee9)
- [Dev Deps] add missing peer dep [`fee274d`](https://github.com/inspect-js/is-date-object/commit/fee274dc177e09823cb69dbcb805cbd4be7a386e)

## [v1.0.5](https://github.com/inspect-js/is-date-object/compare/v1.0.4...v1.0.5) - 2021-08-05

### Commits

- [meta] remove `.jscs.json` [`31c731c`](https://github.com/inspect-js/is-date-object/commit/31c731c5efc5b1b86e6426d904373dc6225b929f)
- [Fix] use `has-tostringtag` to behave correctly in the presence of symbol shams [`17a6df4`](https://github.com/inspect-js/is-date-object/commit/17a6df4a3ab9bcb1395a638ced14f571f9549427)
- [Dev Deps] update `eslint`, `auto-changelog`, `tape` [`79db3af`](https://github.com/inspect-js/is-date-object/commit/79db3af1a745042a0a11e03c7dd7db910b5e0d01)

## [v1.0.4](https://github.com/inspect-js/is-date-object/compare/v1.0.3...v1.0.4) - 2021-05-07

### Commits

- [Fix] do not use `Object.prototype.toString` when `Symbol.toStringTag` is shammed [`8943a4a`](https://github.com/inspect-js/is-date-object/commit/8943a4a5035b3f2c8cee9a5edabb55579c16983d)
- [readme] make all URLs https [`1d4d6cd`](https://github.com/inspect-js/is-date-object/commit/1d4d6cd37365c3a36f98e3f82cfe6262227437db)
- [Dev Deps] update `eslint` [`a7abeaa`](https://github.com/inspect-js/is-date-object/commit/a7abeaa2409d3a34fccebcb5b362e0b90d0a8883)

## [v1.0.3](https://github.com/inspect-js/is-date-object/compare/v1.0.2...v1.0.3) - 2021-05-05

### Commits

- [Tests] migrate tests to Github Actions [`023504f`](https://github.com/inspect-js/is-date-object/commit/023504f4d48fc8788ff52ee525a1d9ec74fa7df5)
- [readme] add actions and codecov badges [`e63305f`](https://github.com/inspect-js/is-date-object/commit/e63305f2fb9ff3eb0dab7e0716585507a4f95a75)
- [meta] do not publish github action workflow files [`017d906`](https://github.com/inspect-js/is-date-object/commit/017d90679b6b1c16b398c0157904f91f56160219)
- [Tests] run `nyc` on all tests [`0376b6f`](https://github.com/inspect-js/is-date-object/commit/0376b6fb7a0ffcc42107c3c579ba0b3ab635b9e4)
- [readme] fix repo URLs; remove defunct badges [`1c148c6`](https://github.com/inspect-js/is-date-object/commit/1c148c6cb6eb0892b3186e814df3367dabb9732d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`c7a3f54`](https://github.com/inspect-js/is-date-object/commit/c7a3f54a207a6056ffafaa58178889ea1b1b77f7)
- [actions] add "Allow Edits" workflow [`e79b5b2`](https://github.com/inspect-js/is-date-object/commit/e79b5b25c173c3201e8b42a614d5f12c48b74a86)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`da28980`](https://github.com/inspect-js/is-date-object/commit/da28980c5fe86528585b2a420319ca8fc35f763a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`5cabae9`](https://github.com/inspect-js/is-date-object/commit/5cabae9f00bf458a470bde68b734540b8ab78c3b)
- [readme] add actions and codecov badges [`33dfb88`](https://github.com/inspect-js/is-date-object/commit/33dfb881b7abf668cd3bf956e2947a1ece552f25)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`745eb04`](https://github.com/inspect-js/is-date-object/commit/745eb0462ef3838df65f41f4a95453cc4f0aa06e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`466c62b`](https://github.com/inspect-js/is-date-object/commit/466c62b45af5a5a83963f6ef8617da887b0ab272)
- [actions] use checkout v2; remove unneeded env [`ff87a16`](https://github.com/inspect-js/is-date-object/commit/ff87a161e36d76d081d70933bf801a357c3b25fe)
- [Dev Deps] update `auto-changelog`, `tape` [`93188f5`](https://github.com/inspect-js/is-date-object/commit/93188f58e4b2c2b5e978a61a45380101d01f9838)
- [meta] use `prepublishOnly` script for npm 7+ [`1d0e3ea`](https://github.com/inspect-js/is-date-object/commit/1d0e3ea672971f02bb48c88b49079789ab41f574)
- [actions] update workflows [`4d1a235`](https://github.com/inspect-js/is-date-object/commit/4d1a2358de35a9fbe23a1dee10735748ed276301)
- [Dev Deps] update `auto-changelog`; add `aud` [`67be59a`](https://github.com/inspect-js/is-date-object/commit/67be59aa3c0ba44b982aaefb7e42adfb14eb279b)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`a6661c2`](https://github.com/inspect-js/is-date-object/commit/a6661c26af701a7782f6e06ad1b34587ce2b09bc)
- [Tests] only audit prod deps [`dd4a47f`](https://github.com/inspect-js/is-date-object/commit/dd4a47f8bcf82c3090826d890a7766f50d6f7af9)

## [v1.0.2](https://github.com/inspect-js/is-date-object/compare/v1.0.1...v1.0.2) - 2019-12-18

### Commits

- [Tests] use shared travis-ci configs [`8a378b8`](https://github.com/inspect-js/is-date-object/commit/8a378b8fd6a4202fffc9ec193aca02efe937bc35)
- [Tests] on all node minors; use `nvm install-latest-npm`; fix scripts; improve matrix [`6e97a21`](https://github.com/inspect-js/is-date-object/commit/6e97a21276cf448ce424fb9ea13edd4587f289f1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is`, `jscs`, `nsp`, `semver`, `tape` [`8472b90`](https://github.com/inspect-js/is-date-object/commit/8472b90f82e5153c22e7a8a7726a5cc6110e93d7)
- [Tests] up to `node` `v10.0`, `v9.11`, `v8.11`, `v6.14`, `v4.9` [`ae73e38`](https://github.com/inspect-js/is-date-object/commit/ae73e3890df7da0bc4449088e30340cb4df3294d)
- [meta] add `auto-changelog` [`82f8f47`](https://github.com/inspect-js/is-date-object/commit/82f8f473a6ee45e2b66810cb743e0122c18381c5)
- [meta] remove unused Makefile and associated utilities [`788a2cd`](https://github.com/inspect-js/is-date-object/commit/788a2cdfd0bc8f1903967219897f6d00c4c6a26b)
- [Tests] up to `node` `v11.4`, `v10.14`, `v8.14`, `v6.15` [`b9caf7c`](https://github.com/inspect-js/is-date-object/commit/b9caf7c814e5e2549454cb444f8b739f9ce1a388)
- [Tests] up to `node` `v12.4`, `v11.15`, `v10.15`, `v8.15`, `v6.17`; use `nvm install-latest-npm` [`cda0abc`](https://github.com/inspect-js/is-date-object/commit/cda0abc04a21c9b5ec72eabd010155c988032056)
- [Tests] up to `node` `v12.10`, `v10.16`, `v8.16` [`49bc482`](https://github.com/inspect-js/is-date-object/commit/49bc482fd9f71436b663c07144083a8423697299)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `semver`, `tape`; add `safe-publish-latest` [`f77fec4`](https://github.com/inspect-js/is-date-object/commit/f77fec48057e156b2276b4c14cf303306116b9f6)
- [actions] add automatic rebasing / merge commit blocking [`68605fc`](https://github.com/inspect-js/is-date-object/commit/68605fcb6bc0341ff0aae14a94bf5d18e1bc73be)
- [meta] create FUNDING.yml [`4f82d88`](https://github.com/inspect-js/is-date-object/commit/4f82d88e1e6ac1b97f0ce96aa0aa057ad758a581)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`3cbf28a`](https://github.com/inspect-js/is-date-object/commit/3cbf28a185ced940cfce8a09fa8479cc83575876)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config@`, `is`, `semver`, `tape` [`abf9fb0`](https://github.com/inspect-js/is-date-object/commit/abf9fb0d55ef0697e64e888d74f2e5fe53d7cdcb)
- [Tests] switch from `nsp` to `npm audit` [`6543c7d`](https://github.com/inspect-js/is-date-object/commit/6543c7d559d1fb79215b46c8b79e0e3e2a83f5de)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`ba5d2d7`](https://github.com/inspect-js/is-date-object/commit/ba5d2d7fc0975d7c03b8f2b7f43a09af93e365ba)
- [Dev Deps] update `eslint`, `nsp`, `semver`, `tape` [`c1e3525`](https://github.com/inspect-js/is-date-object/commit/c1e3525afa76a696f7cf1b58aab7f55d220b2c20)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`14e4824`](https://github.com/inspect-js/is-date-object/commit/14e4824188c85207ed3b86627b09e9f64b135db7)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest` [`68ead64`](https://github.com/inspect-js/is-date-object/commit/68ead64a07e0de282ea3cd38e12cc8b0e0f6d3cd)
- [Dev Deps] update `eslint`, semver`, `tape`, `semver` [`f55453f`](https://github.com/inspect-js/is-date-object/commit/f55453f200903277465d7e9307a9c49120a4f419)
- Only apps should have lockfiles [`6c848eb`](https://github.com/inspect-js/is-date-object/commit/6c848eba982cc58053d4cca08c01f12a433f3695)
- [Tests] remove `jscs` [`3fd3a62`](https://github.com/inspect-js/is-date-object/commit/3fd3a62121607ad074b7fc977f3fc6575b66f755)
- [Dev Deps] update `eslint`, `tape` [`77d3130`](https://github.com/inspect-js/is-date-object/commit/77d3130a0039e5dae24c17de790dd510c265edc6)
- [meta] add `funding` field [`9ef6d58`](https://github.com/inspect-js/is-date-object/commit/9ef6d5888bf829a5812b3b091dc99839d48c355e)

## [v1.0.1](https://github.com/inspect-js/is-date-object/compare/v1.0.0...v1.0.1) - 2015-09-27

### Commits

- Update `tape`, `semver`, `eslint`; use my personal shared `eslint` config. [`731aa13`](https://github.com/inspect-js/is-date-object/commit/731aa134b0b8dc84e302d0b2264a415cb456ccab)
- Update `is`, `tape`, `covert`, `jscs`, `editorconfig-tools`, `nsp`, `eslint`, `semver` [`53e43a6`](https://github.com/inspect-js/is-date-object/commit/53e43a627dd01757cf3d469599f3dffd9d72b150)
- Update `eslint` [`d2fc304`](https://github.com/inspect-js/is-date-object/commit/d2fc3046f087b0026448ffde0cf46b1f741cbd4e)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`c9568df`](https://github.com/inspect-js/is-date-object/commit/c9568df228fa698dc6fcc9553b5d612e7ee427aa)
- Test on latest `node` and `io.js` versions. [`a21d537`](https://github.com/inspect-js/is-date-object/commit/a21d537562166ebd18bde3a262fd157dd774ae17)
- Update `nsp`, `eslint`, `semver` [`9e1d908`](https://github.com/inspect-js/is-date-object/commit/9e1d9087c0c79c34fcb2abfc701cdfa1efcb327c)
- Update `covert`, `jscs`, `eslint`, `semver` [`f198f6b`](https://github.com/inspect-js/is-date-object/commit/f198f6b997912da10a3d821a089e1581edc730a0)
- [Dev Deps] update `tape`, `jscs`, `eslint` [`ab9bdbb`](https://github.com/inspect-js/is-date-object/commit/ab9bdbbc189cef033346508db47cd1feb04a69d3)
- If `@@toStringTag` is not present, use the old-school `Object#toString` test. [`c03afce`](https://github.com/inspect-js/is-date-object/commit/c03afce001368b29eb929900075749b113a252c8)
- [Dev Deps] update `jscs`, `nsp`, `tape`, `eslint`, `@ljharb/eslint-config` [`9d94ccb`](https://github.com/inspect-js/is-date-object/commit/9d94ccbab4160d2fa649123e37951d86b69a8b15)
- [Dev Deps] update `is`, `eslint`, `@ljharb/eslint-config`, `semver` [`35cbff7`](https://github.com/inspect-js/is-date-object/commit/35cbff7f7c8216fbb79c799f74b2336eaf0d726a)
- Test up to `io.js` `v2.3` [`be5d11e`](https://github.com/inspect-js/is-date-object/commit/be5d11e7ebd9473d7ae554179b3769082485f6f4)
- [Tests] on `io.js` `v3.3`, up to `node` `v4.1` [`20221a3`](https://github.com/inspect-js/is-date-object/commit/20221a34858d2b21e23bdc2c08df23f0bc08d11e)
- [Tests] up to `io.js` `v3.2	` [`7009b4a`](https://github.com/inspect-js/is-date-object/commit/7009b4a9999e14eacbdf6068afd82f478473f007)
- Test on `io.js` `v2.1` [`68b29b1`](https://github.com/inspect-js/is-date-object/commit/68b29b19a07e6589a7ca37ab764be28f144ac88e)
- Remove `editorconfig-tools` [`8d3972c`](https://github.com/inspect-js/is-date-object/commit/8d3972c1795fdcfd337680e11ab610e4885fb079)
- [Dev Deps] update `tape` [`204945d`](https://github.com/inspect-js/is-date-object/commit/204945d8658a3513ca6315ddf795e4034adb4545)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`7bff214`](https://github.com/inspect-js/is-date-object/commit/7bff214dcb2317b96219921476f990814afbb401)
- Test on `io.js` `v2.5` [`92f7bd6`](https://github.com/inspect-js/is-date-object/commit/92f7bd6747e3259b0ddc9c287876f46a9cd4c270)
- Test on `io.js` `v2.4` [`ebb34bf`](https://github.com/inspect-js/is-date-object/commit/ebb34bf1f58949768063f86ac012f1ca5d7cf6d9)
- Fix tests for faked @@toStringTag [`3b9c26c`](https://github.com/inspect-js/is-date-object/commit/3b9c26c15040af6a87f8d77ce6c85a7bef7a4304)
- Test on `io.js` `v3.0` [`5eedf4b`](https://github.com/inspect-js/is-date-object/commit/5eedf4bea76380a08813fd0977469c2480302a82)

## v1.0.0 - 2015-01-28

### Commits

- Dotfiles. [`5b6a929`](https://github.com/inspect-js/is-date-object/commit/5b6a9298c6f70882e78e66d64c9c019f85790f52)
- `make release` [`e8d40ce`](https://github.com/inspect-js/is-date-object/commit/e8d40ceca85acd0aa4b2753faa6e41c0c54cf6c3)
- package.json [`a107259`](https://github.com/inspect-js/is-date-object/commit/a1072591ea510a2998298be6cef827b123f4643f)
- Read me [`eb92695`](https://github.com/inspect-js/is-date-object/commit/eb92695664bdee8fc49891cd73aa2f41075f53cb)
- Initial commit [`4fc7755`](https://github.com/inspect-js/is-date-object/commit/4fc7755ff12f1d7a55cf841d486bf6b2350fe5a0)
- Tests. [`b6f432f`](https://github.com/inspect-js/is-date-object/commit/b6f432fb6801c5ff8d89cfec7601d59478e23dd1)
- Implementation. [`dd0fd96`](https://github.com/inspect-js/is-date-object/commit/dd0fd96c4016a66cec7cd59db0fde37c2ef3cdb5)
