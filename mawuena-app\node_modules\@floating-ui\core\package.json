{"name": "@floating-ui/core", "version": "1.7.1", "description": "Positioning library for floating elements: tooltips, popovers, dropdowns, and more", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.core.umd.js", "module": "./dist/floating-ui.core.esm.js", "unpkg": "./dist/floating-ui.core.umd.min.js", "types": "./dist/floating-ui.core.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.core.d.mts", "default": "./dist/floating-ui.core.mjs"}, "types": "./dist/floating-ui.core.d.ts", "module": "./dist/floating-ui.core.esm.js", "default": "./dist/floating-ui.core.umd.js"}}, "sideEffects": false, "files": ["dist"], "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/core"}, "homepage": "https://floating-ui.com", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning"], "dependencies": {"@floating-ui/utils": "^0.2.9"}, "devDependencies": {"config": "0.0.0"}, "scripts": {"test": "vitest run", "test:watch": "vitest watch", "lint": "eslint .", "format": "prettier --write .", "clean": "<PERSON><PERSON><PERSON> dist out-tsc", "dev": "rollup -c -w", "build": "rollup -c", "build:api": "build-api --tsc tsconfig.lib.json", "publint": "publint", "typecheck": "tsc -b"}}