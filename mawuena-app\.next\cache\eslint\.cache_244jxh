[{"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx": "1", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx": "2", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\client.ts": "3", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\server.ts": "4", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\utils.ts": "5", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\middleware.ts": "6", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\auth-code-error\\page.tsx": "7", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\callback\\route.ts": "8", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx": "9", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\actions.ts": "10", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx": "11", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\alert.tsx": "12", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\button.tsx": "13", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\card.tsx": "14", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\input.tsx": "15", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx": "16", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx": "17", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\database\\index.ts": "18", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\types\\database.ts": "19", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\bulk-upload\\page.tsx": "20", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\page.tsx": "21", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\inventory\\BulkUploadForm.tsx": "22", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\layout\\DashboardLayout.tsx": "23", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\select.tsx": "24"}, {"size": 689, "mtime": 1750306578773, "results": "25", "hashOfConfig": "26"}, {"size": 333, "mtime": 1750308802298, "results": "27", "hashOfConfig": "26"}, {"size": 212, "mtime": 1750307339246, "results": "28", "hashOfConfig": "26"}, {"size": 790, "mtime": 1750307349206, "results": "29", "hashOfConfig": "26"}, {"size": 166, "mtime": 1750307221017, "results": "30", "hashOfConfig": "26"}, {"size": 2292, "mtime": 1750307768433, "results": "31", "hashOfConfig": "26"}, {"size": 2516, "mtime": 1750309988471, "results": "32", "hashOfConfig": "26"}, {"size": 1196, "mtime": 1750308570810, "results": "33", "hashOfConfig": "26"}, {"size": 5703, "mtime": 1750310482169, "results": "34", "hashOfConfig": "26"}, {"size": 2436, "mtime": 1750309325966, "results": "35", "hashOfConfig": "26"}, {"size": 5904, "mtime": 1750309433486, "results": "36", "hashOfConfig": "26"}, {"size": 1614, "mtime": 1750308537974, "results": "37", "hashOfConfig": "26"}, {"size": 2123, "mtime": 1750308537733, "results": "38", "hashOfConfig": "26"}, {"size": 1989, "mtime": 1750308537895, "results": "39", "hashOfConfig": "26"}, {"size": 967, "mtime": 1750308537827, "results": "40", "hashOfConfig": "26"}, {"size": 611, "mtime": 1750308537856, "results": "41", "hashOfConfig": "26"}, {"size": 1969, "mtime": 1750308537946, "results": "42", "hashOfConfig": "26"}, {"size": 1136, "mtime": 1750309357674, "results": "43", "hashOfConfig": "26"}, {"size": 10383, "mtime": 1750308095455, "results": "44", "hashOfConfig": "26"}, {"size": 1460, "mtime": 1750310249401, "results": "45", "hashOfConfig": "26"}, {"size": 4446, "mtime": 1750310548397, "results": "46", "hashOfConfig": "26"}, {"size": 11207, "mtime": 1750310289799, "results": "47", "hashOfConfig": "26"}, {"size": 1635, "mtime": 1750310499632, "results": "48", "hashOfConfig": "26"}, {"size": 6253, "mtime": 1750310397869, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kvcvg0", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\client.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\server.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\utils.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\middleware.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\auth-code-error\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\callback\\route.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\actions.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\alert.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\button.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\card.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\input.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\database\\index.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\types\\database.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\bulk-upload\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\inventory\\BulkUploadForm.tsx", ["122"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\layout\\DashboardLayout.tsx", ["123"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\select.tsx", [], [], {"ruleId": "124", "severity": 2, "message": "125", "line": 29, "column": 42, "nodeType": null, "messageId": "126", "endLine": 29, "endColumn": 48}, {"ruleId": "124", "severity": 2, "message": "127", "line": 15, "column": 73, "nodeType": null, "messageId": "126", "endLine": 15, "endColumn": 77}, "@typescript-eslint/no-unused-vars", "'userId' is defined but never used.", "unusedVar", "'user' is defined but never used."]