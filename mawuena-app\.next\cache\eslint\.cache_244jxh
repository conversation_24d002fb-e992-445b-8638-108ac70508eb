[{"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx": "1", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx": "2", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\client.ts": "3", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\server.ts": "4", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\utils.ts": "5", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\middleware.ts": "6", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\auth-code-error\\page.tsx": "7", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\callback\\route.ts": "8", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx": "9", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\actions.ts": "10", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx": "11", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\alert.tsx": "12", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\button.tsx": "13", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\card.tsx": "14", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\input.tsx": "15", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx": "16", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx": "17", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\database\\index.ts": "18", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\types\\database.ts": "19"}, {"size": 689, "mtime": 1750306578773, "results": "20", "hashOfConfig": "21"}, {"size": 333, "mtime": 1750308802298, "results": "22", "hashOfConfig": "21"}, {"size": 212, "mtime": 1750307339246, "results": "23", "hashOfConfig": "21"}, {"size": 790, "mtime": 1750307349206, "results": "24", "hashOfConfig": "21"}, {"size": 166, "mtime": 1750307221017, "results": "25", "hashOfConfig": "21"}, {"size": 2292, "mtime": 1750307768433, "results": "26", "hashOfConfig": "21"}, {"size": 2515, "mtime": 1750308586789, "results": "27", "hashOfConfig": "21"}, {"size": 1196, "mtime": 1750308570810, "results": "28", "hashOfConfig": "21"}, {"size": 5144, "mtime": 1750308609466, "results": "29", "hashOfConfig": "21"}, {"size": 2436, "mtime": 1750309325966, "results": "30", "hashOfConfig": "21"}, {"size": 5904, "mtime": 1750309433486, "results": "31", "hashOfConfig": "21"}, {"size": 1614, "mtime": 1750308537974, "results": "32", "hashOfConfig": "21"}, {"size": 2123, "mtime": 1750308537733, "results": "33", "hashOfConfig": "21"}, {"size": 1989, "mtime": 1750308537895, "results": "34", "hashOfConfig": "21"}, {"size": 967, "mtime": 1750308537827, "results": "35", "hashOfConfig": "21"}, {"size": 611, "mtime": 1750308537856, "results": "36", "hashOfConfig": "21"}, {"size": 1969, "mtime": 1750308537946, "results": "37", "hashOfConfig": "21"}, {"size": 1136, "mtime": 1750309357674, "results": "38", "hashOfConfig": "21"}, {"size": 10383, "mtime": 1750308095455, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kvcvg0", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\client.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\server.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\utils.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\middleware.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\auth-code-error\\page.tsx", ["97"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\callback\\route.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\actions.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\alert.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\button.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\card.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\input.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\database\\index.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\types\\database.ts", [], [], {"ruleId": "98", "severity": 2, "message": "99", "line": 27, "column": 24, "nodeType": "100", "messageId": "101", "suggestions": "102"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["103", "104", "105", "106"], {"messageId": "107", "data": "108", "fix": "109", "desc": "110"}, {"messageId": "107", "data": "111", "fix": "112", "desc": "113"}, {"messageId": "107", "data": "114", "fix": "115", "desc": "116"}, {"messageId": "107", "data": "117", "fix": "118", "desc": "119"}, "replaceWithAlt", {"alt": "120"}, {"range": "121", "text": "122"}, "Replace with `&apos;`.", {"alt": "123"}, {"range": "124", "text": "125"}, "Replace with `&lsquo;`.", {"alt": "126"}, {"range": "127", "text": "128"}, "Replace with `&#39;`.", {"alt": "129"}, {"range": "130", "text": "131"}, "Replace with `&rsquo;`.", "&apos;", [1035, 1111], "\n              We couldn&apos;t complete your authentication request\n            ", "&lsquo;", [1035, 1111], "\n              We couldn&lsquo;t complete your authentication request\n            ", "&#39;", [1035, 1111], "\n              We couldn&#39;t complete your authentication request\n            ", "&rsquo;", [1035, 1111], "\n              We couldn&rsquo;t complete your authentication request\n            "]