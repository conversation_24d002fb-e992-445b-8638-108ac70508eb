[{"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx": "1", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx": "2", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\client.ts": "3", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\server.ts": "4", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\utils.ts": "5", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\middleware.ts": "6", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\auth-code-error\\page.tsx": "7", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\callback\\route.ts": "8", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx": "9", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\actions.ts": "10", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx": "11", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\alert.tsx": "12", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\button.tsx": "13", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\card.tsx": "14", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\input.tsx": "15", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx": "16", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx": "17", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\database\\index.ts": "18", "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\types\\database.ts": "19"}, {"size": 689, "mtime": 1750306578773, "results": "20", "hashOfConfig": "21"}, {"size": 333, "mtime": 1750308802298, "results": "22", "hashOfConfig": "21"}, {"size": 212, "mtime": 1750307339246, "results": "23", "hashOfConfig": "21"}, {"size": 790, "mtime": 1750307349206, "results": "24", "hashOfConfig": "21"}, {"size": 166, "mtime": 1750307221017, "results": "25", "hashOfConfig": "21"}, {"size": 2292, "mtime": 1750307768433, "results": "26", "hashOfConfig": "21"}, {"size": 2515, "mtime": 1750308586789, "results": "27", "hashOfConfig": "21"}, {"size": 1196, "mtime": 1750308570810, "results": "28", "hashOfConfig": "21"}, {"size": 5144, "mtime": 1750308609466, "results": "29", "hashOfConfig": "21"}, {"size": 2444, "mtime": 1750309131604, "results": "30", "hashOfConfig": "21"}, {"size": 10221, "mtime": 1750308921852, "results": "31", "hashOfConfig": "21"}, {"size": 1614, "mtime": 1750308537974, "results": "32", "hashOfConfig": "21"}, {"size": 2123, "mtime": 1750308537733, "results": "33", "hashOfConfig": "21"}, {"size": 1989, "mtime": 1750308537895, "results": "34", "hashOfConfig": "21"}, {"size": 967, "mtime": 1750308537827, "results": "35", "hashOfConfig": "21"}, {"size": 611, "mtime": 1750308537856, "results": "36", "hashOfConfig": "21"}, {"size": 1969, "mtime": 1750308537946, "results": "37", "hashOfConfig": "21"}, {"size": 1320, "mtime": 1750309167918, "results": "38", "hashOfConfig": "21"}, {"size": 10383, "mtime": 1750308095455, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kvcvg0", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\client.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\supabase\\server.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\utils.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\middleware.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\auth-code-error\\page.tsx", ["97"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\auth\\callback\\route.ts", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\actions.ts", ["98"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\login\\page.tsx", ["99"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\alert.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\button.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\card.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\input.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\label.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\components\\ui\\tabs.tsx", [], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\database\\index.ts", ["100", "101", "102", "103"], [], "D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\lib\\types\\database.ts", [], [], {"ruleId": "104", "severity": 2, "message": "105", "line": 27, "column": 24, "nodeType": "106", "messageId": "107", "suggestions": "108"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 32, "column": 14, "nodeType": null, "messageId": "111", "endLine": 32, "endColumn": 19}, {"ruleId": "109", "severity": 2, "message": "112", "line": 11, "column": 10, "nodeType": null, "messageId": "111", "endLine": 11, "endColumn": 19}, {"ruleId": "109", "severity": 2, "message": "113", "line": 5, "column": 6, "nodeType": null, "messageId": "111", "endLine": 5, "endColumn": 18}, {"ruleId": "109", "severity": 2, "message": "114", "line": 6, "column": 6, "nodeType": null, "messageId": "111", "endLine": 6, "endColumn": 14}, {"ruleId": "109", "severity": 2, "message": "115", "line": 7, "column": 6, "nodeType": null, "messageId": "111", "endLine": 7, "endColumn": 10}, {"ruleId": "109", "severity": 2, "message": "116", "line": 8, "column": 6, "nodeType": null, "messageId": "111", "endLine": 8, "endColumn": 19}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["117", "118", "119", "120"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "'LoginForm' is defined but never used.", "'ClothingItem' is defined but never used.", "'Customer' is defined but never used.", "'Sale' is defined but never used.", "'InstagramPost' is defined but never used.", {"messageId": "121", "data": "122", "fix": "123", "desc": "124"}, {"messageId": "121", "data": "125", "fix": "126", "desc": "127"}, {"messageId": "121", "data": "128", "fix": "129", "desc": "130"}, {"messageId": "121", "data": "131", "fix": "132", "desc": "133"}, "replaceWithAlt", {"alt": "134"}, {"range": "135", "text": "136"}, "Replace with `&apos;`.", {"alt": "137"}, {"range": "138", "text": "139"}, "Replace with `&lsquo;`.", {"alt": "140"}, {"range": "141", "text": "142"}, "Replace with `&#39;`.", {"alt": "143"}, {"range": "144", "text": "145"}, "Replace with `&rsquo;`.", "&apos;", [1035, 1111], "\n              We couldn&apos;t complete your authentication request\n            ", "&lsquo;", [1035, 1111], "\n              We couldn&lsquo;t complete your authentication request\n            ", "&#39;", [1035, 1111], "\n              We couldn&#39;t complete your authentication request\n            ", "&rsquo;", [1035, 1111], "\n              We couldn&rsquo;t complete your authentication request\n            "]