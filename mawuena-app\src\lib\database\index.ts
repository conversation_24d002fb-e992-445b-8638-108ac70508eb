import { createClient } from '@/lib/supabase/server'
import { Database } from '@/lib/types/database'

type Tables = Database['public']['Tables']

// Helper function to get supabase client
async function getSupabase() {
  return await createClient()
}

// User operations
export async function createUser(userData: Tables['users']['Insert']) {
  const supabase = await getSupabase()
  const { data, error } = await supabase
    .from('users')
    .insert(userData)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function getUser(userId: string) {
  const supabase = await getSupabase()
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) throw error
  return data
}

export async function updateUser(userId: string, updates: Tables['users']['Update']) {
  const supabase = await getSupabase()
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

// More database functions can be added here as needed...
