import { createClient } from '@/lib/supabase/server'
import { Database } from '@/lib/types/database'

type Tables = Database['public']['Tables']
type ClothingItem = Tables['clothing_items']['Row']
type Customer = Tables['customers']['Row']
type Sale = Tables['sales']['Row']
type InstagramPost = Tables['instagram_posts']['Row']

export class DatabaseService {
  private supabase

  constructor() {
    this.supabase = createClient()
  }

  // User operations
  async createUser(userData: Tables['users']['Insert']) {
    const { data, error } = await this.supabase
      .from('users')
      .insert(userData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getUser(userId: string) {
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) throw error
    return data
  }

  async updateUser(userId: string, updates: Tables['users']['Update']) {
    const { data, error } = await this.supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Clothing items operations
  async createClothingItem(itemData: Tables['clothing_items']['Insert']) {
    const { data, error } = await this.supabase
      .from('clothing_items')
      .insert(itemData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getClothingItems(userId: string, status?: ClothingItem['status']) {
    let query = this.supabase
      .from('clothing_items')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) throw error
    return data
  }

  async getClothingItem(itemId: string) {
    const { data, error } = await this.supabase
      .from('clothing_items')
      .select('*')
      .eq('id', itemId)
      .single()

    if (error) throw error
    return data
  }

  async updateClothingItem(itemId: string, updates: Tables['clothing_items']['Update']) {
    const { data, error } = await this.supabase
      .from('clothing_items')
      .update(updates)
      .eq('id', itemId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deleteClothingItem(itemId: string) {
    const { error } = await this.supabase
      .from('clothing_items')
      .delete()
      .eq('id', itemId)

    if (error) throw error
  }

  async bulkCreateClothingItems(items: Tables['clothing_items']['Insert'][]) {
    const { data, error } = await this.supabase
      .from('clothing_items')
      .insert(items)
      .select()

    if (error) throw error
    return data
  }

  // Customer operations
  async createCustomer(customerData: Tables['customers']['Insert']) {
    const { data, error } = await this.supabase
      .from('customers')
      .insert(customerData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getCustomers(userId: string) {
    const { data, error } = await this.supabase
      .from('customers')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  async getCustomerByInstagram(userId: string, instagramUsername: string) {
    const { data, error } = await this.supabase
      .from('customers')
      .select('*')
      .eq('user_id', userId)
      .eq('instagram_username', instagramUsername)
      .single()

    if (error && error.code !== 'PGRST116') throw error // PGRST116 is "not found"
    return data
  }

  async updateCustomer(customerId: string, updates: Tables['customers']['Update']) {
    const { data, error } = await this.supabase
      .from('customers')
      .update(updates)
      .eq('id', customerId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Sales operations
  async createSale(saleData: Tables['sales']['Insert']) {
    const { data, error } = await this.supabase
      .from('sales')
      .insert(saleData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getSales(userId: string, status?: Sale['status']) {
    let query = this.supabase
      .from('sales')
      .select(`
        *,
        clothing_items(*),
        customers(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) throw error
    return data
  }

  async updateSale(saleId: string, updates: Tables['sales']['Update']) {
    const { data, error } = await this.supabase
      .from('sales')
      .update(updates)
      .eq('id', saleId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Instagram posts operations
  async createInstagramPost(postData: Tables['instagram_posts']['Insert']) {
    const { data, error } = await this.supabase
      .from('instagram_posts')
      .insert(postData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getInstagramPosts(userId: string) {
    const { data, error } = await this.supabase
      .from('instagram_posts')
      .select(`
        *,
        clothing_items(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  async updateInstagramPost(postId: string, updates: Tables['instagram_posts']['Update']) {
    const { data, error } = await this.supabase
      .from('instagram_posts')
      .update(updates)
      .eq('id', postId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Analytics operations
  async getUserInventoryStats(userId: string) {
    const { data, error } = await this.supabase
      .rpc('get_user_inventory_stats', { user_uuid: userId })

    if (error) throw error
    return data[0] || {
      total_items: 0,
      draft_items: 0,
      ready_items: 0,
      posted_items: 0,
      sold_items: 0,
      total_value: 0
    }
  }

  async getRecentCustomerActivity(userId: string, daysBack: number = 7) {
    const { data, error } = await this.supabase
      .rpc('get_recent_customer_activity', { 
        user_uuid: userId, 
        days_back: daysBack 
      })

    if (error) throw error
    return data || []
  }

  // Conversation operations
  async createConversation(conversationData: Tables['conversations']['Insert']) {
    const { data, error } = await this.supabase
      .from('conversations')
      .insert(conversationData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getConversations(userId: string, status?: Tables['conversations']['Row']['status']) {
    let query = this.supabase
      .from('conversations')
      .select(`
        *,
        customers(*)
      `)
      .eq('user_id', userId)
      .order('last_activity', { ascending: false })

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) throw error
    return data
  }

  async updateConversation(conversationId: string, updates: Tables['conversations']['Update']) {
    const { data, error } = await this.supabase
      .from('conversations')
      .update(updates)
      .eq('id', conversationId)
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Export a singleton instance
export const db = new DatabaseService()
