import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Upload, Download, FileSpreadsheet } from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import Link from 'next/link'

export default async function InventoryPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect('/login')
  }

  // TODO: Fetch user's clothing items from database
  const items = [] // Placeholder

  return (
    <DashboardLayout
      title="Inventory"
      description="Manage your clothing items and upload new inventory"
      user={data.user}
    >
      <div className="flex justify-end gap-3 mb-6">
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
        <Button asChild>
          <Link href="/dashboard/inventory/bulk-upload">
            <Plus className="h-4 w-4 mr-2" />
            Add Items
          </Link>
        </Button>
      </div>
          {items.length === 0 ? (
            // Empty State
            <Card className="text-center py-12">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                  <FileSpreadsheet className="h-6 w-6 text-gray-400" />
                </div>
                <CardTitle>No items in inventory</CardTitle>
                <CardDescription>
                  Get started by uploading your first clothing items
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button asChild>
                    <Link href="/dashboard/inventory/bulk-upload">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Items
                    </Link>
                  </Button>
                  <Button variant="outline">
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Import from CSV
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-4">
                  You can upload multiple images at once and fill in details using our spreadsheet interface
                </p>
              </CardContent>
            </Card>
          ) : (
            // Items List/Grid
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-xs text-muted-foreground">Total Items</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-xs text-muted-foreground">Ready to Post</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-xs text-muted-foreground">Posted</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold">₦0</div>
                    <p className="text-xs text-muted-foreground">Total Value</p>
                  </CardContent>
                </Card>
              </div>

              {/* Items Table/Grid will go here */}
              <Card>
                <CardHeader>
                  <CardTitle>Your Items</CardTitle>
                  <CardDescription>
                    Manage your clothing inventory
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500">Items will be displayed here...</p>
                </CardContent>
              </Card>
            </div>
          )}
    </DashboardLayout>
  )
}
