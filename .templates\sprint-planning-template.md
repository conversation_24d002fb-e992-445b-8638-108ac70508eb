# Sprint Planning Template

**Instructions**: Review Augment Agent's sprint proposal and provide your priorities and decisions below.

## Sprint Overview

**Sprint Number:** [X]
**Proposed Duration:** [1-2 weeks]
**Sprint Goal:** [What we want to achieve this sprint]

## Feature Prioritization

### Proposed Features for This Sprint
**Augment Agent's Recommendations:**
```
[Agent will fill this in with recommended features and technical tasks]
```

### Your Priority Decisions
**Rank these features in order of importance (1 = highest priority):**
```
__ [Feature A]
__ [Feature B] 
__ [Feature C]
__ [Feature D]
[Add more as needed]
```

**Any features to add to this sprint?**
```
[Features you want to include that weren't proposed]
```

**Any features to remove or postpone?**
```
[Features to move to a later sprint]
```

### Acceptance Criteria
**For each high-priority feature, define what "done" looks like:**

**Feature 1: [Name]**
```
Done when:
- [Specific criteria 1]
- [Specific criteria 2]
- [Specific criteria 3]
```

**Feature 2: [Name]**
```
Done when:
- [Specific criteria 1]
- [Specific criteria 2]
- [Specific criteria 3]
```

**Feature 3: [Name]**
```
Done when:
- [Specific criteria 1]
- [Specific criteria 2]
- [Specific criteria 3]
```

## Business Decisions Needed

### Feature Behavior
**How should [specific feature] work?**
```
[Describe the expected behavior for any complex features]
```

### User Experience Decisions
**Any specific UX requirements for this sprint's features?**
```
[Specific user experience requirements or preferences]
```

### Business Logic
**Any business rules that need to be implemented?**
```
[Specific business logic or rules for the features]
```

## Success Metrics

### Sprint Success Criteria
**How will we know this sprint was successful?**
```
[Specific, measurable criteria for sprint success]
```

### Testing Requirements
**What level of testing do you want for this sprint?**
```
□ Basic functionality testing
□ Comprehensive testing including edge cases
□ User acceptance testing required
□ Performance testing needed
□ Security testing required
```

## Timeline and Availability

### Your Availability
**How available will you be during this sprint for:**

**Daily check-ins:**
```
□ Available daily
□ Available every 2-3 days
□ Available weekly
□ As-needed basis
```

**Feature reviews:**
```
□ Can review immediately when ready
□ Need 24-48 hours for review
□ Can only review on specific days: [specify]
```

**Decision making:**
```
□ Can make decisions quickly (same day)
□ Need time to consider (2-3 days)
□ Need to consult with others first
```

### External Dependencies
**Are there any external factors that might affect this sprint?**
```
[Examples: waiting for content, third-party approvals, etc.]
```

## Risk Assessment

### Potential Blockers
**What could prevent us from completing this sprint successfully?**
```
[Any concerns or potential issues you foresee]
```

### Mitigation Plans
**How should we handle potential issues?**
```
[Your preferences for handling problems or delays]
```

## Communication Preferences

### Progress Updates
**How do you want to receive progress updates during this sprint?**
```
□ Daily brief updates
□ Every 2-3 days
□ Weekly comprehensive updates
□ Only when issues arise
```

### Review Process
**How do you want to review completed features?**
```
□ Live demo/walkthrough
□ Screenshots and written description
□ Access to working version to test yourself
□ Combination of above
```

## Sprint Approval

### Final Sprint Plan
```
□ I approve this sprint plan as outlined
□ I approve with the modifications specified above
□ I need to discuss some aspects before approval

Comments:
[Any additional thoughts or concerns]
```

### Authorization to Proceed
```
□ Yes, begin this sprint immediately
□ Yes, but start on [specific date]
□ No, let's revise the plan first

Next steps:
[Any actions needed before starting the sprint]
```

---

## Quick Reference: Your Decisions Summary

**Top 3 Priorities This Sprint:**
1. [Priority 1]
2. [Priority 2] 
3. [Priority 3]

**Key Business Decisions Made:**
- [Decision 1]
- [Decision 2]
- [Decision 3]

**Review Schedule:**
- [When you'll review progress]
- [When you'll approve completed features]

**Success Criteria:**
- [How we'll measure sprint success]

---

**Once completed, Augment Agent will begin sprint execution based on your priorities and decisions.**
