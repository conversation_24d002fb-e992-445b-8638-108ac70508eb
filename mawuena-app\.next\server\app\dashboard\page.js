(()=>{var e={};e.id=105,e.ids=[105],e.modules={559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(7413),a=r(9916),i=r(2507),s=r(2359),o=r(3469),l=r(8963);async function c(){let e=await (0,i.U)(),{data:t,error:r}=await e.auth.getUser();return(r||!t?.user)&&(0,a.redirect)("/login"),(0,n.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,n.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,n.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:["Welcome back, ",t.user.email]})]}),(0,n.jsx)("form",{action:s.S6,children:(0,n.jsx)(o.$,{variant:"outline",type:"submit",children:"Sign Out"})})]})}),(0,n.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,n.jsxs)(l.Zp,{className:"col-span-full",children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsx)(l.ZB,{children:"Welcome to Mawuena! \uD83C\uDF89"}),(0,n.jsx)(l.BT,{children:"Your Instagram clothing business management platform is ready to go."})]}),(0,n.jsx)(l.Wu,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Get started by setting up your Instagram connection and uploading your first items."}),(0,n.jsxs)("div",{className:"flex gap-3",children:[(0,n.jsx)(o.$,{children:"Connect Instagram"}),(0,n.jsx)(o.$,{variant:"outline",children:"Upload Items"})]})]})})]}),(0,n.jsxs)(l.Zp,{children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsx)(l.ZB,{children:"Inventory"}),(0,n.jsx)(l.BT,{children:"Your clothing items"})]}),(0,n.jsxs)(l.Wu,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold",children:"0"}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"items in inventory"})]})]}),(0,n.jsxs)(l.Zp,{children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsx)(l.ZB,{children:"Posts"}),(0,n.jsx)(l.BT,{children:"Instagram posts"})]}),(0,n.jsxs)(l.Wu,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold",children:"0"}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"posts published"})]})]}),(0,n.jsxs)(l.Zp,{children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsx)(l.ZB,{children:"Sales"}),(0,n.jsx)(l.BT,{children:"Total revenue"})]}),(0,n.jsxs)(l.Wu,{children:[(0,n.jsx)("div",{className:"text-2xl font-bold",children:"₦0"}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"total sales"})]})]}),(0,n.jsxs)(l.Zp,{className:"col-span-full",children:[(0,n.jsxs)(l.aR,{children:[(0,n.jsx)(l.ZB,{children:"Next Steps"}),(0,n.jsx)(l.BT,{children:"Complete these steps to get the most out of Mawuena"})]}),(0,n.jsx)(l.Wu,{children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,n.jsx)("span",{className:"text-sm",children:"Connect your Instagram Business account"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"Upload your first clothing items"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"Set up your AI agent personality"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),(0,n.jsx)("span",{className:"text-sm text-gray-500",children:"Create your first automated post"})]})]})})]})]})})]})})}},826:(e,t,r)=>{"use strict";Object.defineProperty(t,"e",{enumerable:!0,get:function(){return u}});let n=r(6143),a=r(7719),i=r(9294),s=r(3033),o=r(3365),l=0;async function c(e,t,r,a,i,s,l){await t.set(r,{kind:o.CachedRouteKind.FETCH,data:{headers:{},body:JSON.stringify(e),status:200,url:""},revalidate:"number"!=typeof i?n.CACHE_ONE_YEAR:i},{fetchCache:!0,tags:a,fetchIdx:s,fetchUrl:l})}function u(e,t,r={}){if(0===r.revalidate)throw Object.defineProperty(Error(`Invariant revalidate: 0 can not be passed to unstable_cache(), must be "false" or "> 0" ${e.toString()}`),"__NEXT_ERROR_CODE",{value:"E57",enumerable:!1,configurable:!0});let n=r.tags?(0,a.validateTags)(r.tags,`unstable_cache ${e.toString()}`):[];(0,a.validateRevalidate)(r.revalidate,`unstable_cache ${e.name||e.toString()}`);let d=`${e.toString()}-${Array.isArray(t)&&t.join(",")}`;return async(...t)=>{let a=i.workAsyncStorage.getStore(),u=s.workUnitAsyncStorage.getStore(),p=(null==a?void 0:a.incrementalCache)||globalThis.__incrementalCache;if(!p)throw Object.defineProperty(Error(`Invariant: incrementalCache missing in unstable_cache ${e.toString()}`),"__NEXT_ERROR_CODE",{value:"E469",enumerable:!1,configurable:!0});let f=u&&"prerender"===u.type?u.cacheSignal:null;f&&f.beginRead();try{let i=u&&"request"===u.type?u:void 0,f=(null==i?void 0:i.url.pathname)??(null==a?void 0:a.route)??"",h=new URLSearchParams((null==i?void 0:i.url.search)??""),g=[...h.keys()].sort((e,t)=>e.localeCompare(t)).map(e=>`${e}=${h.get(e)}`).join("&"),m=`${d}-${JSON.stringify(t)}`,b=await p.generateCacheKey(m),v=`unstable_cache ${f}${g.length?"?":""}${g} ${e.name?` ${e.name}`:b}`,y=(a?a.nextFetchId:l)??1,_=null==u?void 0:u.implicitTags,x={type:"unstable-cache",phase:"render",implicitTags:_,draftMode:u&&a&&(0,s.getDraftModeProviderForCacheScope)(a,u)};if(a){if(a.nextFetchId=y+1,u&&("cache"===u.type||"prerender"===u.type||"prerender-ppr"===u.type||"prerender-legacy"===u.type)){"number"==typeof r.revalidate&&(u.revalidate<r.revalidate||(u.revalidate=r.revalidate));let e=u.tags;if(null===e)u.tags=n.slice();else for(let t of n)e.includes(t)||e.push(t)}if(!(u&&"unstable-cache"===u.type)&&"force-no-store"!==a.fetchCache&&!a.isOnDemandRevalidate&&!p.isOnDemandRevalidate&&!a.isDraftMode){let i=await p.get(b,{kind:o.IncrementalCacheKind.FETCH,revalidate:r.revalidate,tags:n,softTags:null==_?void 0:_.tags,fetchIdx:y,fetchUrl:v});if(i&&i.value)if(i.value.kind!==o.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${m}`);else{let o=void 0!==i.value.data.body?JSON.parse(i.value.data.body):void 0;return i.isStale&&(a.pendingRevalidates||(a.pendingRevalidates={}),a.pendingRevalidates[m]=s.workUnitAsyncStorage.run(x,e,...t).then(e=>c(e,p,b,n,r.revalidate,y,v)).catch(e=>console.error(`revalidating cache with key: ${m}`,e))),o}}let i=await s.workUnitAsyncStorage.run(x,e,...t);return a.isDraftMode||c(i,p,b,n,r.revalidate,y,v),i}{if(l+=1,!p.isOnDemandRevalidate){let e=await p.get(b,{kind:o.IncrementalCacheKind.FETCH,revalidate:r.revalidate,tags:n,fetchIdx:y,fetchUrl:v,softTags:null==_?void 0:_.tags});if(e&&e.value){if(e.value.kind!==o.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${m}`);else if(!e.isStale)return void 0!==e.value.data.body?JSON.parse(e.value.data.body):void 0}}let a=await s.workUnitAsyncStorage.run(x,e,...t);return c(a,p,b,n,r.revalidate,y,v),a}}finally{f&&f.endRead()}}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var n=r(5986),a=r(8974);function i(...e){return(0,a.QP)((0,n.$)(e))}},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2351:(e,t,r)=>{let n={unstable_cache:r(826).e,revalidateTag:r(3518).revalidateTag,revalidatePath:r(3518).revalidatePath,unstable_expireTag:r(3518).unstable_expireTag,unstable_expirePath:r(3518).unstable_expirePath,unstable_noStore:r(8553).M,unstable_cacheLife:r(4128).F,unstable_cacheTag:r(2932).z};e.exports=n,t.unstable_cache=n.unstable_cache,t.revalidatePath=n.revalidatePath,t.revalidateTag=n.revalidateTag,t.unstable_expireTag=n.unstable_expireTag,t.unstable_expirePath=n.unstable_expirePath,t.unstable_noStore=n.unstable_noStore,t.unstable_cacheLife=n.unstable_cacheLife,t.unstable_cacheTag=n.unstable_cacheTag},2359:(e,t,r)=>{"use strict";r.d(t,{iD:()=>u,S6:()=>p,$5:()=>d});var n=r(7218);r(9130);var a=r(2351),i=r(9916),s=r(2507);async function o(){return await (0,s.U)()}async function l(e){let t=await o(),{data:r,error:n}=await t.from("users").insert(e).select().single();if(n)throw n;return r}async function c(e){let t=await o(),{data:r,error:n}=await t.from("users").select("*").eq("id",e).single();if(n)throw n;return r}async function u(e){let t=await (0,s.U)(),r=e.get("email"),n=e.get("password");r&&n||(0,i.redirect)("/login?error=missing-credentials");let{data:o,error:u}=await t.auth.signInWithPassword({email:r,password:n});if(u&&(0,i.redirect)(`/login?error=${encodeURIComponent(u.message)}`),o.user)try{await c(o.user.id)}catch{await l({id:o.user.id,email:o.user.email,instagram_handle:null,business_name:null,mastra_agent_config:{}})}(0,a.revalidatePath)("/","layout"),(0,i.redirect)("/dashboard")}async function d(e){let t=await (0,s.U)(),r=e.get("email"),n=e.get("password"),o=e.get("business_name");r&&n||(0,i.redirect)("/login?error=missing-credentials");let{data:c,error:u}=await t.auth.signUp({email:r,password:n,options:{emailRedirectTo:`${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`}});if(u&&(0,i.redirect)(`/login?error=${encodeURIComponent(u.message)}`),c.user)try{await l({id:c.user.id,email:c.user.email,instagram_handle:null,business_name:o||null,mastra_agent_config:{}})}catch(e){console.error("Error creating user profile:",e)}(0,a.revalidatePath)("/","layout"),(0,i.redirect)("/login?message=check-email")}async function p(){let e=await (0,s.U)(),{error:t}=await e.auth.signOut();t&&(0,i.redirect)("/error"),(0,a.revalidatePath)("/","layout"),(0,i.redirect)("/login")}(0,r(7478).D)([u,d,p]),(0,n.A)(u,"404ffb6408f3758ef54519a18c70686620246b4b96",null),(0,n.A)(d,"403b9a20870bf87801d4fe1364ea25b88da2e64fba",null),(0,n.A)(p,"00f2852461826a1a20213f241ac21fd695c07bc6e1",null)},2602:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return o},decrypt:function(){return u},encrypt:function(){return c},getActionEncryptionKey:function(){return g},getClientReferenceManifestForRsc:function(){return h},getServerModuleMap:function(){return f},setReferenceManifestsSingleton:function(){return p},stringToUint8Array:function(){return l}});let a=r(1617),i=r(4722),s=r(9294);function o(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let n="";for(let e=0;e<r;e++)n+=String.fromCharCode(t[e]);return n}function l(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r}function c(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function u(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}let d=Symbol.for("next.server.action-manifests");function p({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:n}){var a;let s=null==(a=globalThis[d])?void 0:a.clientReferenceManifestsPerPage;globalThis[d]={clientReferenceManifestsPerPage:{...s,[(0,i.normalizeAppPath)(e)]:t},serverActionsManifest:r,serverModuleMap:n}}function f(){let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function h(){let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,r=s.workAsyncStorage.getStore();if(!r){var n=t;let e=Object.values(n),r={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)r.clientModules={...r.clientModules,...t.clientModules},r.edgeRscModuleMapping={...r.edgeRscModuleMapping,...t.edgeRscModuleMapping},r.rscModuleMapping={...r.rscModuleMapping,...t.rscModuleMapping};return r}let i=t[r.route];if(!i)throw Object.defineProperty(new a.InvariantError(`Missing Client Reference Manifest for ${r.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return i}async function g(){if(n)return n;let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new a.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",l(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},2932:(e,t,r)=>{"use strict";function n(...e){throw Object.defineProperty(Error("cacheTag() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E628",enumerable:!1,configurable:!0})}Object.defineProperty(t,"z",{enumerable:!0,get:function(){return n}}),r(3033),r(7719)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3469:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(7413);r(1120);var a=r(403),i=r(662),s=r(974);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:i=!1,...l}){let c=i?a.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,s.cn)(o({variant:t,size:r,className:e})),...l})}},3496:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return a.isDynamicRoute}});let n=r(9610),a=r(9022)},3518:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{revalidatePath:function(){return p},revalidateTag:function(){return c},unstable_expirePath:function(){return u},unstable_expireTag:function(){return d}});let n=r(4971),a=r(3496),i=r(6143),s=r(9294),o=r(3033),l=r(8479);function c(e){return f([e],`revalidateTag ${e}`)}function u(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: expirePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`);let r=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?r+=`${r.endsWith("/")?"":"/"}${t}`:(0,a.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "expirePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`),f([r],`unstable_expirePath ${e}`)}function d(...e){return f(e,`unstable_expireTag ${e.join(", ")}`)}function p(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: revalidatePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`);let r=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?r+=`${r.endsWith("/")?"":"/"}${t}`:(0,a.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "revalidatePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`),f([r],`revalidatePath ${e}`)}function f(e,t){let r=s.workAsyncStorage.getStore();if(!r||!r.incrementalCache)throw Object.defineProperty(Error(`Invariant: static generation store missing in ${t}`),"__NEXT_ERROR_CODE",{value:"E263",enumerable:!1,configurable:!0});let a=o.workUnitAsyncStorage.getStore();if(a){if("cache"===a.type)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" inside a "use cache" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E181",enumerable:!1,configurable:!0});if("unstable-cache"===a.type)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" inside a function cached with "unstable_cache(...)" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E306",enumerable:!1,configurable:!0});if("render"===a.phase)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E7",enumerable:!1,configurable:!0});if("prerender"===a.type){let e=Object.defineProperty(Error(`Route ${r.route} used ${t} without first calling \`await connection()\`.`),"__NEXT_ERROR_CODE",{value:"E406",enumerable:!1,configurable:!0});(0,n.abortAndThrowOnSynchronousRequestDataAccess)(r.route,t,e,a)}else if("prerender-ppr"===a.type)(0,n.postponeWithTracking)(r.route,t,a.dynamicTracking);else if("prerender-legacy"===a.type){a.revalidate=0;let e=Object.defineProperty(new l.DynamicServerError(`Route ${r.route} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.dynamicUsageDescription=t,r.dynamicUsageStack=e.stack,e}}for(let t of(r.pendingRevalidatedTags||(r.pendingRevalidatedTags=[]),e))r.pendingRevalidatedTags.includes(t)||r.pendingRevalidatedTags.push(t);r.pathWasRevalidated=!0}},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4128:(e,t,r)=>{"use strict";function n(e){throw Object.defineProperty(Error("cacheLife() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E627",enumerable:!1,configurable:!0})}Object.defineProperty(t,"F",{enumerable:!0,get:function(){return n}}),r(9294),r(3033)},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4924:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00f2852461826a1a20213f241ac21fd695c07bc6e1":()=>n.S6,"403b9a20870bf87801d4fe1364ea25b88da2e64fba":()=>n.$5,"404ffb6408f3758ef54519a18c70686620246b4b96":()=>n.iD});var n=r(2359)},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=r(5239),a=r(8088),i=r(8170),s=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,559)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7218:(e,t,r)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=r(2907)},7478:(e,t)=>{"use strict";function r(e){for(let t=0;t<e.length;t++){let r=e[t];if("function"!=typeof r)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof r}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return r}})},7910:e=>{"use strict";e.exports=require("stream")},8553:(e,t,r)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return s}});let n=r(9294),a=r(3033),i=r(4971);function s(){let e=n.workAsyncStorage.getStore(),t=a.workUnitAsyncStorage.getStore();if(e)!e.forceStatic&&(e.isUnstableNoStore=!0,t&&"prerender"===t.type||(0,i.markCurrentScopeAsDynamic)(e,t,"unstable_noStore()"))}},8963:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>s});var n=r(7413);r(1120);var a=r(974);function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},9022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return s}});let n=r(1437),a=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,i=/\/\[[^/]+\](?=\/|$)/;function s(e,t){return(void 0===t&&(t=!0),(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),t)?i.test(e):a.test(e)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9130:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return g},encryptActionBoundArgs:function(){return h}}),r(4822);let n=r(2907),a=r(2513),i=r(7855),s=r(2602),o=r(3033),l=r(4971),c=function(e){return e&&e.__esModule?e:{default:e}}(r(1120)),u=new TextEncoder,d=new TextDecoder;async function p(e,t){let r=await (0,s.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(t),a=n.slice(0,16),i=n.slice(16),o=d.decode(await (0,s.decrypt)(r,(0,s.stringToUint8Array)(a),(0,s.stringToUint8Array)(i)));if(!o.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return o.slice(e.length)}async function f(e,t){let r=await (0,s.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);o.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let a=(0,s.arrayBufferToString)(n.buffer),i=await (0,s.encrypt)(r,n,u.encode(e+t));return btoa(a+(0,s.arrayBufferToString)(i))}let h=c.default.cache(async function e(t,...r){let{clientModules:a}=(0,s.getClientReferenceManifestForRsc)(),c=Error();Error.captureStackTrace(c,e);let u=!1,d=o.workUnitAsyncStorage.getStore(),p=(null==d?void 0:d.type)==="prerender"?(0,l.createHangingInputAbortSignal)(d):void 0,h=await (0,i.streamToString)((0,n.renderToReadableStream)(r,a,{signal:p,onError(e){(null==p||!p.aborted)&&(u||(u=!0,c.message=e instanceof Error?e.message:String(e)))}}),p);if(u)throw c;if(!d)return f(t,h);let g=(0,o.getPrerenderResumeDataCache)(d),m=(0,o.getRenderResumeDataCache)(d),b=t+h,v=(null==g?void 0:g.encryptedBoundArgs.get(b))??(null==m?void 0:m.encryptedBoundArgs.get(b));if(v)return v;let y="prerender"===d.type?d.cacheSignal:void 0;null==y||y.beginRead();let _=await f(t,h);return null==y||y.endRead(),null==g||g.encryptedBoundArgs.set(b,_),_});async function g(e,t){let r,n=await t,i=o.workUnitAsyncStorage.getStore();if(i){let t="prerender"===i.type?i.cacheSignal:void 0,a=(0,o.getPrerenderResumeDataCache)(i),s=(0,o.getRenderResumeDataCache)(i);(r=(null==a?void 0:a.decryptedBoundArgs.get(n))??(null==s?void 0:s.decryptedBoundArgs.get(n)))||(null==t||t.beginRead(),r=await p(e,n),null==t||t.endRead(),null==a||a.decryptedBoundArgs.set(n,r))}else r=await p(e,n);let{edgeRscModuleMapping:l,rscModuleMapping:c}=(0,s.getClientReferenceManifestForRsc)();return await (0,a.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(u.encode(r)),(null==i?void 0:i.type)==="prerender"?i.renderSignal.aborted?e.close():i.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:c,serverModuleMap:(0,s.getServerModuleMap)()}})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return a},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let a=e[0];if(a.startsWith("[")&&a.endsWith("]")){let r=a.slice(1,-1),s=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),s=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function i(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===a.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(n)if(s){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});i(this.optionalRestSlugName,r),this.optionalRestSlugName=r,a="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});i(this.restSlugName,r),this.restSlugName=r,a="[...]"}else{if(s)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});i(this.slugName,r),this.slugName=r,a="[]"}}this.children.has(a)||this.children.set(a,new r),this.children.get(a)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function a(e,t){let r={},a=[];for(let n=0;n<e.length;n++){let i=t(e[n]);r[i]=n,a[n]=i}return n(a).map(t=>e[r[t]])}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,145,658,875,923,271],()=>r(5919));module.exports=n})();