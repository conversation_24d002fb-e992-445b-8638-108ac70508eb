(()=>{var e={};e.id=105,e.ids=[105],e.modules={440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},559:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(7413),a=s(9916),i=s(2507),n=s(2359),o=s(3469),d=s(8963);async function l(){let e=await (0,i.U)(),{data:r,error:s}=await e.auth.getUser();return(s||!r?.user)&&(0,a.redirect)("/login"),(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,t.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:["Welcome back, ",r.user.email]})]}),(0,t.jsx)("form",{action:n.S6,children:(0,t.jsx)(o.$,{variant:"outline",type:"submit",children:"Sign Out"})})]})}),(0,t.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(d.Zp,{className:"col-span-full",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Welcome to Mawuena! \uD83C\uDF89"}),(0,t.jsx)(d.BT,{children:"Your Instagram clothing business management platform is ready to go."})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Get started by setting up your Instagram connection and uploading your first items."}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(o.$,{children:"Connect Instagram"}),(0,t.jsx)(o.$,{variant:"outline",children:"Upload Items"})]})]})})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Inventory"}),(0,t.jsx)(d.BT,{children:"Your clothing items"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"0"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"items in inventory"})]})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Posts"}),(0,t.jsx)(d.BT,{children:"Instagram posts"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"0"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"posts published"})]})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Sales"}),(0,t.jsx)(d.BT,{children:"Total revenue"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"₦0"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"total sales"})]})]}),(0,t.jsxs)(d.Zp,{className:"col-span-full",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Next Steps"}),(0,t.jsx)(d.BT,{children:"Complete these steps to get the most out of Mawuena"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Connect your Instagram Business account"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Upload your first clothing items"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Set up your AI agent personality"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Create your first automated post"})]})]})})]})]})})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,r,s)=>{"use strict";s.d(r,{cn:()=>i});var t=s(5986),a=s(8974);function i(...e){return(0,a.QP)((0,t.$)(e))}},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=1906,e.exports=r},1997:e=>{"use strict";e.exports=require("punycode")},2295:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},2359:(e,r,s)=>{"use strict";s.d(r,{iD:()=>c,S6:()=>x,$5:()=>u});var t=s(7218);s(9130);var a=s(2351),i=s(9916),n=s(2507);async function o(){return await (0,n.U)()}async function d(e){let r=await o(),{data:s,error:t}=await r.from("users").insert(e).select().single();if(t)throw t;return s}async function l(e){let r=await o(),{data:s,error:t}=await r.from("users").select("*").eq("id",e).single();if(t)throw t;return s}async function c(e){let r=await (0,n.U)(),s=e.get("email"),t=e.get("password");s&&t||(0,i.redirect)("/login?error=missing-credentials");let{data:o,error:c}=await r.auth.signInWithPassword({email:s,password:t});if(c&&(0,i.redirect)(`/login?error=${encodeURIComponent(c.message)}`),o.user)try{await l(o.user.id)}catch{await d({id:o.user.id,email:o.user.email,instagram_handle:null,business_name:null,mastra_agent_config:{}})}(0,a.revalidatePath)("/","layout"),(0,i.redirect)("/dashboard")}async function u(e){let r=await (0,n.U)(),s=e.get("email"),t=e.get("password"),o=e.get("business_name");s&&t||(0,i.redirect)("/login?error=missing-credentials");let{data:l,error:c}=await r.auth.signUp({email:s,password:t,options:{emailRedirectTo:`${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`}});if(c&&(0,i.redirect)(`/login?error=${encodeURIComponent(c.message)}`),l.user)try{await d({id:l.user.id,email:l.user.email,instagram_handle:null,business_name:o||null,mastra_agent_config:{}})}catch(e){console.error("Error creating user profile:",e)}(0,a.revalidatePath)("/","layout"),(0,i.redirect)("/login?message=check-email")}async function x(){let e=await (0,n.U)(),{error:r}=await e.auth.signOut();r&&(0,i.redirect)("/error"),(0,a.revalidatePath)("/","layout"),(0,i.redirect)("/login")}(0,s(7478).D)([c,u,x]),(0,t.A)(c,"404ffb6408f3758ef54519a18c70686620246b4b96",null),(0,t.A)(u,"403b9a20870bf87801d4fe1364ea25b88da2e64fba",null),(0,t.A)(x,"00f2852461826a1a20213f241ac21fd695c07bc6e1",null)},2507:(e,r,s)=>{"use strict";s.d(r,{U:()=>i});var t=s(261),a=s(4999);async function i(){let e=await (0,a.UL)();return(0,t.createServerClient)(process.env.NEXT_PUBLIC_SUPABASE_URL,process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:s,options:t})=>e.set(r,s,t))}catch{}}}})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3178:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3346:()=>{},3469:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(7413);s(1120);var a=s(403),i=s(662),n=s(974);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:s,asChild:i=!1,...d}){let l=i?a.DX:"button";return(0,t.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:s,className:e})),...d})}},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l,metadata:()=>d});var t=s(7413),a=s(2376),i=s.n(a),n=s(8726),o=s.n(n);s(1135);let d={title:"Create Next App",description:"Generated by create next app"};function l({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:e})})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4924:(e,r,s)=>{"use strict";s.r(r),s.d(r,{"00f2852461826a1a20213f241ac21fd695c07bc6e1":()=>t.S6,"403b9a20870bf87801d4fe1364ea25b88da2e64fba":()=>t.$5,"404ffb6408f3758ef54519a18c70686620246b4b96":()=>t.iD});var t=s(2359)},5343:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5919:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var t=s(5239),a=s(8088),i=s(8170),n=s.n(i),o=s(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,559)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},7990:()=>{},8335:()=>{},8963:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var t=s(7413);s(1120);var a=s(974);function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9727:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,145,875,658,923,877],()=>s(5919));module.exports=t})();