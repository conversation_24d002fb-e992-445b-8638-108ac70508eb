[{"name": "generate-buildid", "duration": 1257, "timestamp": 16108988136, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750309384606, "traceId": "bbee4532234e9a47"}, {"name": "load-custom-routes", "duration": 1924, "timestamp": 16108989931, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750309384607, "traceId": "bbee4532234e9a47"}, {"name": "create-dist-dir", "duration": 2015, "timestamp": 16109543081, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750309385161, "traceId": "bbee4532234e9a47"}, {"name": "create-pages-mapping", "duration": 1510, "timestamp": 16109710503, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750309385328, "traceId": "bbee4532234e9a47"}, {"name": "collect-app-paths", "duration": 28988, "timestamp": 16109712223, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750309385330, "traceId": "bbee4532234e9a47"}, {"name": "create-app-mapping", "duration": 31048, "timestamp": 16109741436, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750309385359, "traceId": "bbee4532234e9a47"}, {"name": "public-dir-conflict-check", "duration": 58626, "timestamp": 16109800317, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750309385418, "traceId": "bbee4532234e9a47"}, {"name": "generate-routes-manifest", "duration": 52566, "timestamp": 16109863953, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750309385481, "traceId": "bbee4532234e9a47"}, {"name": "next-build", "duration": 18381055, "timestamp": 16108129178, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.4", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1750309383747, "traceId": "bbee4532234e9a47"}]