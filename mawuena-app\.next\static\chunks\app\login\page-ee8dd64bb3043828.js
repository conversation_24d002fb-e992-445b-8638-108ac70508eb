(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1897:(e,t,n)=>{"use strict";n.d(t,{Tabs:()=>$,TabsContent:()=>Y,TabsList:()=>J,TabsTrigger:()=>X});var r=n(5155),o=n(2115),a=n(5185),i=n(6081),s=n(7328),l=n(6101),u=n(1285),c=n(3655),d=n(9033),f=n(5845),m=n(4315),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,w,h]=(0,s.N)(b),[y,x]=(0,i.A)(b,[h]),[N,T]=y(b),I=o.forwardRef((e,t)=>(0,r.jsx)(g.<PERSON><PERSON>,{scope:e.__scopeRovingFocusGroup,children:(0,r.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,r.jsx)(A,{...e,ref:t})})}));I.displayName=b;var A=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:s=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:y,onEntryFocus:x,preventScrollOnEntryFocus:T=!1,...I}=e,A=o.useRef(null),R=(0,l.s)(t,A),j=(0,m.jH)(u),[E,F]=(0,f.i)({prop:g,defaultProp:null!=h?h:null,onChange:y,caller:b}),[M,C]=o.useState(!1),O=(0,d.c)(x),k=w(n),_=o.useRef(!1),[L,P]=o.useState(0);return o.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,r.jsx)(N,{scope:n,orientation:i,dir:j,loop:s,currentTabStopId:E,onItemFocus:o.useCallback(e=>F(e),[F]),onItemShiftTab:o.useCallback(()=>C(!0),[]),onFocusableItemAdd:o.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>P(e=>e-1),[]),children:(0,r.jsx)(c.sG.div,{tabIndex:M||0===L?-1:0,"data-orientation":i,...I,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!_.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),T)}}_.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>C(!1))})})}),R="RovingFocusGroupItem",j=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:s=!1,tabStopId:l,children:d,...f}=e,m=(0,u.B)(),p=l||m,v=T(R,n),b=v.currentTabStopId===p,h=w(n),{onFocusableItemAdd:y,onFocusableItemRemove:x,currentTabStopId:N}=v;return o.useEffect(()=>{if(i)return y(),()=>x()},[i,y,x]),(0,r.jsx)(g.ItemSlot,{scope:n,id:p,focusable:i,active:s,children:(0,r.jsx)(c.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{i?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>D(n))}}),children:"function"==typeof d?d({isCurrentTabStop:b,hasTabStop:null!=N}):d})})});j.displayName=R;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var F=n(2712),M=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,a]=o.useState(),i=o.useRef(null),s=o.useRef(e),l=o.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=C(i.current);l.current="mounted"===u?e:"none"},[u]),(0,F.N)(()=>{let t=i.current,n=s.current;if(n!==e){let r=l.current,o=C(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),(0,F.N)(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=C(i.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!s.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},a=e=>{e.target===r&&(l.current=C(i.current))};return r.addEventListener("animationstart",a),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",a),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),i=(0,l.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||r.isPresent?o.cloneElement(a,{ref:i}):null};function C(e){return(null==e?void 0:e.animationName)||"none"}M.displayName="Presence";var O="Tabs",[k,_]=(0,i.A)(O,[x]),L=x(),[P,S]=k(O),U=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,onValueChange:a,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:d="automatic",...p}=e,v=(0,m.jH)(l),[b,g]=(0,f.i)({prop:o,onChange:a,defaultProp:null!=i?i:"",caller:O});return(0,r.jsx)(P,{scope:n,baseId:(0,u.B)(),value:b,onValueChange:g,orientation:s,dir:v,activationMode:d,children:(0,r.jsx)(c.sG.div,{dir:v,"data-orientation":s,...p,ref:t})})});U.displayName=O;var G="TabsList",K=o.forwardRef((e,t)=>{let{__scopeTabs:n,loop:o=!0,...a}=e,i=S(G,n),s=L(n);return(0,r.jsx)(I,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:o,children:(0,r.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});K.displayName=G;var B="TabsTrigger",V=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,disabled:i=!1,...s}=e,l=S(B,n),u=L(n),d=H(l.baseId,o),f=q(l.baseId,o),m=o===l.value;return(0,r.jsx)(j,{asChild:!0,...u,focusable:!i,active:m,children:(0,r.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":f,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...s,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(o)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(o)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||i||!e||l.onValueChange(o)})})})});V.displayName=B;var z="TabsContent",W=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,forceMount:i,children:s,...l}=e,u=S(z,n),d=H(u.baseId,a),f=q(u.baseId,a),m=a===u.value,p=o.useRef(m);return o.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(M,{present:i||m,children:n=>{let{present:o}=n;return(0,r.jsx)(c.sG.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!o,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:o&&s})}})});function H(e,t){return"".concat(e,"-trigger-").concat(t)}function q(e,t){return"".concat(e,"-content-").concat(t)}W.displayName=z;var Q=n(9434);function $(e){let{className:t,...n}=e;return(0,r.jsx)(U,{"data-slot":"tabs",className:(0,Q.cn)("flex flex-col gap-2",t),...n})}function J(e){let{className:t,...n}=e;return(0,r.jsx)(K,{"data-slot":"tabs-list",className:(0,Q.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...n})}function X(e){let{className:t,...n}=e;return(0,r.jsx)(V,{"data-slot":"tabs-trigger",className:(0,Q.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}function Y(e){let{className:t,...n}=e;return(0,r.jsx)(W,{"data-slot":"tabs-content",className:(0,Q.cn)("flex-1 outline-none",t),...n})}},2282:(e,t,n)=>{Promise.resolve().then(n.bind(n,5057)),Promise.resolve().then(n.bind(n,1897))},5057:(e,t,n)=>{"use strict";n.d(t,{Label:()=>i});var r=n(5155);n(2115);var o=n(968),a=n(9434);function i(e){let{className:t,...n}=e;return(0,r.jsx)(o.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...n})}},9434:(e,t,n)=>{"use strict";n.d(t,{cn:()=>a});var r=n(2596),o=n(9688);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,o.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[349,441,684,358],()=>t(2282)),_N_E=e.O()}]);