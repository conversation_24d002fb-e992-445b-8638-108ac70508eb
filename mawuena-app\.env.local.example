# Application Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Instagram API Configuration
INSTAGRAM_APP_ID=your_instagram_app_id
INSTAGRAM_APP_SECRET=your_instagram_app_secret
INSTAGRAM_REDIRECT_URI=http://localhost:3000/auth/instagram/callback

# Mastra AI Configuration (when we add it)
MASTRA_API_KEY=your_mastra_api_key

# OpenAI Configuration (for AI features)
OPENAI_API_KEY=your_openai_api_key

# Trigger.dev Configuration (when we add it)
TRIGGER_DEV_API_KEY=your_trigger_dev_api_key

# Paystack Configuration (when we add it)
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# PostHog Configuration (when we add it)
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
