-- Seed data for testing (only run in development)
-- This file should be run manually or through Supabase dashboard

-- Note: In production, users will be created through Supabase Auth
-- This is just for testing purposes

-- Insert sample automation rules templates
INSERT INTO public.automation_rules (id, user_id, name, trigger_type, action_type, conditions, mastra_tool_config, active) VALUES
  (
    uuid_generate_v4(),
    '00000000-0000-0000-0000-000000000000', -- Placeholder user_id
    'Auto-respond to price inquiries',
    'dm_received',
    'send_response',
    '{"keywords": ["price", "cost", "how much"]}',
    '{"response_template": "Thank you for your interest! The price is {item_price}. Would you like to purchase it?"}',
    true
  ),
  (
    uuid_generate_v4(),
    '00000000-0000-0000-0000-000000000000', -- Placeholder user_id
    'Auto-post new items',
    'item_added',
    'create_instagram_post',
    '{"status": "ready"}',
    '{"caption_template": "New arrival! {item_name} by {brand}. Size: {size}. Price: ₦{price}. DM to order! #fashion #style"}',
    true
  );

-- Sample clothing categories for reference
CREATE TABLE IF NOT EXISTS public.clothing_categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO public.clothing_categories (name, description) VALUES
  ('Dresses', 'All types of dresses - casual, formal, party'),
  ('Tops', 'Shirts, blouses, t-shirts, tank tops'),
  ('Bottoms', 'Pants, jeans, skirts, shorts'),
  ('Outerwear', 'Jackets, coats, blazers, cardigans'),
  ('Footwear', 'Shoes, sandals, boots, sneakers'),
  ('Accessories', 'Bags, jewelry, scarves, belts'),
  ('Activewear', 'Gym clothes, sportswear, athleisure'),
  ('Lingerie', 'Underwear, bras, sleepwear'),
  ('Traditional', 'Traditional African wear, cultural clothing'),
  ('Plus Size', 'Plus size clothing options');

-- Sample size chart for reference
CREATE TABLE IF NOT EXISTS public.size_chart (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  size_code TEXT NOT NULL,
  size_name TEXT NOT NULL,
  category TEXT NOT NULL,
  measurements JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO public.size_chart (size_code, size_name, category, measurements) VALUES
  ('XS', 'Extra Small', 'general', '{"bust": "32", "waist": "24", "hips": "34"}'),
  ('S', 'Small', 'general', '{"bust": "34", "waist": "26", "hips": "36"}'),
  ('M', 'Medium', 'general', '{"bust": "36", "waist": "28", "hips": "38"}'),
  ('L', 'Large', 'general', '{"bust": "38", "waist": "30", "hips": "40"}'),
  ('XL', 'Extra Large', 'general', '{"bust": "40", "waist": "32", "hips": "42"}'),
  ('XXL', '2X Large', 'general', '{"bust": "42", "waist": "34", "hips": "44"}'),
  ('6', 'Size 6', 'shoes', '{"length": "23.5cm", "width": "8.5cm"}'),
  ('7', 'Size 7', 'shoes', '{"length": "24.5cm", "width": "9cm"}'),
  ('8', 'Size 8', 'shoes', '{"length": "25.5cm", "width": "9.5cm"}'),
  ('9', 'Size 9', 'shoes', '{"length": "26.5cm", "width": "10cm"}'),
  ('10', 'Size 10', 'shoes', '{"length": "27.5cm", "width": "10.5cm"}');

-- Sample brand list for reference
CREATE TABLE IF NOT EXISTS public.brands (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  logo_url TEXT,
  website TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO public.brands (name, description) VALUES
  ('Zara', 'Spanish fast fashion retailer'),
  ('H&M', 'Swedish multinational clothing retailer'),
  ('Nike', 'American multinational corporation for athletic footwear and apparel'),
  ('Adidas', 'German multinational corporation for athletic footwear and apparel'),
  ('Forever 21', 'American fast fashion retailer'),
  ('Mango', 'Spanish clothing design and manufacturing company'),
  ('Uniqlo', 'Japanese casual wear designer and retailer'),
  ('Local Brand', 'Local Nigerian fashion brands'),
  ('Vintage', 'Vintage and second-hand items'),
  ('Handmade', 'Handmade and artisanal items');

-- Sample Instagram post templates
CREATE TABLE IF NOT EXISTS public.post_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  template TEXT NOT NULL,
  category TEXT,
  variables JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO public.post_templates (name, template, category, variables) VALUES
  (
    'New Arrival',
    'New arrival! ✨ {item_name} by {brand}. Size: {size}. Price: ₦{price}. DM to order! #fashion #style #newcollection',
    'new_item',
    '["item_name", "brand", "size", "price"]'
  ),
  (
    'Sale Item',
    '🔥 SALE ALERT! {item_name} now ₦{price} (was ₦{original_price}). Size: {size}. Limited time offer! DM now! #sale #fashion #discount',
    'sale',
    '["item_name", "price", "original_price", "size"]'
  ),
  (
    'Restock',
    'Back in stock! 📦 {item_name} by {brand}. Popular size {size} available again. Price: ₦{price}. DM to secure yours! #restock #fashion',
    'restock',
    '["item_name", "brand", "size", "price"]'
  ),
  (
    'Last Piece',
    'Last piece! ⚡ {item_name} in size {size}. Don\'t miss out! Price: ₦{price}. DM immediately! #lastpiece #fashion #urgent',
    'urgent',
    '["item_name", "size", "price"]'
  );

-- Sample AI response templates
CREATE TABLE IF NOT EXISTS public.ai_response_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trigger_keywords TEXT[] DEFAULT '{}',
  response_template TEXT NOT NULL,
  category TEXT,
  priority INTEGER DEFAULT 1,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO public.ai_response_templates (trigger_keywords, response_template, category, priority) VALUES
  (
    ARRAY['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening'],
    'Hello! 👋 Welcome to our fashion store. How can I help you today?',
    'greeting',
    1
  ),
  (
    ARRAY['price', 'cost', 'how much', 'amount'],
    'The price for this item is ₦{item_price}. Would you like to purchase it? I can send you a secure payment link! 💳',
    'pricing',
    2
  ),
  (
    ARRAY['size', 'sizes', 'what sizes', 'available sizes'],
    'This item is available in size {item_size}. Would you like to check if we have other sizes available? 📏',
    'sizing',
    2
  ),
  (
    ARRAY['buy', 'purchase', 'order', 'want to buy', 'i want this'],
    'Great choice! 🛍️ I\'ll create a secure payment link for you. The total is ₦{item_price}. You can pay with card, bank transfer, or mobile money.',
    'purchase',
    3
  ),
  (
    ARRAY['delivery', 'shipping', 'when will it arrive', 'how long'],
    'We offer delivery within Lagos (₦1,500) and nationwide shipping (₦2,500). Orders are typically delivered within 2-5 business days. 🚚',
    'delivery',
    2
  ),
  (
    ARRAY['return', 'exchange', 'refund', 'not satisfied'],
    'We have a 7-day return policy for unworn items with tags. Please contact us if you need to return or exchange your purchase. 🔄',
    'returns',
    2
  );

-- Create functions for common operations
CREATE OR REPLACE FUNCTION get_user_inventory_stats(user_uuid UUID)
RETURNS TABLE (
  total_items BIGINT,
  draft_items BIGINT,
  ready_items BIGINT,
  posted_items BIGINT,
  sold_items BIGINT,
  total_value BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_items,
    COUNT(*) FILTER (WHERE status = 'draft') as draft_items,
    COUNT(*) FILTER (WHERE status = 'ready') as ready_items,
    COUNT(*) FILTER (WHERE status = 'posted') as posted_items,
    COUNT(*) FILTER (WHERE status = 'sold') as sold_items,
    COALESCE(SUM(price), 0) as total_value
  FROM public.clothing_items 
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get recent customer activity
CREATE OR REPLACE FUNCTION get_recent_customer_activity(user_uuid UUID, days_back INTEGER DEFAULT 7)
RETURNS TABLE (
  customer_name TEXT,
  instagram_username TEXT,
  last_message_at TIMESTAMP WITH TIME ZONE,
  total_purchases BIGINT,
  total_spent BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.name,
    c.instagram_username,
    conv.last_activity,
    COUNT(s.id) as total_purchases,
    COALESCE(SUM(s.amount), 0) as total_spent
  FROM public.customers c
  LEFT JOIN public.conversations conv ON c.id = conv.customer_id
  LEFT JOIN public.sales s ON c.id = s.customer_id
  WHERE c.user_id = user_uuid 
    AND conv.last_activity >= NOW() - INTERVAL '%s days' % days_back
  GROUP BY c.id, c.name, c.instagram_username, conv.last_activity
  ORDER BY conv.last_activity DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
