# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/ljharb/unbox-primitive/compare/v1.0.2...v1.1.0) - 2024-12-15

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`348a5ad`](https://github.com/ljharb/unbox-primitive/commit/348a5ad5d353e7fcd6353934073ca3a982f40a4e)
- [New] add types [`a324230`](https://github.com/ljharb/unbox-primitive/commit/a32423092f81bfbf3c4dff77a5fa549ffae87daa)
- [Tests] use `es-value-fixtures` [`a321ae5`](https://github.com/ljharb/unbox-primitive/commit/a321ae56aaf30ea843c708bf6c6c13ee52eee205)
- [actions] split out node 10-20, and 20+ [`04a0e0d`](https://github.com/ljharb/unbox-primitive/commit/04a0e0d09907c2643e25ad69b49864c1c34e59d0)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `object-inspect`, `object-is`, `tape` [`7fff971`](https://github.com/ljharb/unbox-primitive/commit/7fff97161bf83ebe348290ef27992af630ca92e9)
- [actions] update rebase action [`91d6807`](https://github.com/ljharb/unbox-primitive/commit/91d6807c562a39ce876feda777b641eb0ee64ce9)
- [Deps] update `call-bind`, `has-symbols`, `which-boxed-primitive` [`f8b6597`](https://github.com/ljharb/unbox-primitive/commit/f8b6597e071d05f0792f6eef0e2fbc30ddfe43d4)
- [Dev Deps] update `aud`, `object-inspect`, `tape` [`b3362a1`](https://github.com/ljharb/unbox-primitive/commit/b3362a164fdbee9006be765d99d5d835d6493326)
- [Refactor] use `call-bound` directly [`e29ff5f`](https://github.com/ljharb/unbox-primitive/commit/e29ff5f4cbcc58d32e670bce11d5c3137eae0c44)
- [meta] add missing `engines.node` [`d3420bc`](https://github.com/ljharb/unbox-primitive/commit/d3420bc8cda471cf62119e08d9581b72962f74f6)
- [Tests] replace `aud` with `npm audit` [`5e6a6d0`](https://github.com/ljharb/unbox-primitive/commit/5e6a6d0459b6fa8a812d00362fe60854333f1b64)
- [Deps] update `which-boxed-primitive` [`0ff873d`](https://github.com/ljharb/unbox-primitive/commit/0ff873dadf31962c2f00e24d8d7d30cfa8c60ac6)
- [Dev Deps] update `aud` [`dd0e373`](https://github.com/ljharb/unbox-primitive/commit/dd0e3733bb9db65d254492d6c1d1b710005b932f)
- [Dev Deps] add missing peer dep [`4f79b24`](https://github.com/ljharb/unbox-primitive/commit/4f79b240212d847276dba9a0044f356506eaeaa6)

## [v1.0.2](https://github.com/ljharb/unbox-primitive/compare/v1.0.1...v1.0.2) - 2022-04-24

### Commits

- [actions] reuse common workflows [`e6420b9`](https://github.com/ljharb/unbox-primitive/commit/e6420b94eed3558ef3ecddbd3d85fe2f400d1db1)
- [actions] update codecov uploader [`b90aff2`](https://github.com/ljharb/unbox-primitive/commit/b90aff2b17b70c1a7200eb5faa3f9e64ebf055f9)
- [readme] add github actions/codecov badges; update URLs [`bcc39b9`](https://github.com/ljharb/unbox-primitive/commit/bcc39b9d6d101bad199c95604f94999f80d6da97)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`a704a32`](https://github.com/ljharb/unbox-primitive/commit/a704a32ec4af43cd0631317ae3a537012adc1610)
- [Refactor] use `call-bind` instead of `function-bind` [`0a609f1`](https://github.com/ljharb/unbox-primitive/commit/0a609f1be6269cbf501482aacd9a0a1bd8596108)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape` [`6a45317`](https://github.com/ljharb/unbox-primitive/commit/6a4531757d74af97c9d0770b102f81ce6ff0a8dc)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`795c76f`](https://github.com/ljharb/unbox-primitive/commit/795c76f14c4df3c7ae934f7b14a98c07951ea9c7)
- [Deps] update `has-bigints`, `has-symbols` [`257a065`](https://github.com/ljharb/unbox-primitive/commit/257a065496b4190c296d674bff342ea40cf8e176)

## [v1.0.1](https://github.com/ljharb/unbox-primitive/compare/v1.0.0...v1.0.1) - 2021-03-25

### Commits

- [Tests] use shared travis-ci configs [`f977e5f`](https://github.com/ljharb/unbox-primitive/commit/f977e5f8fa532dbc519bd78a48cf4b81c14720fe)
- [Tests] migrate tests to Github Actions [`b89def6`](https://github.com/ljharb/unbox-primitive/commit/b89def60908a236aa1b5c756426f7cc61cf458dd)
- [meta] do not publish github action workflow files [`325d1f1`](https://github.com/ljharb/unbox-primitive/commit/325d1f1836cecbe57ee148545de5aefcbe7a7dce)
- readme [`810cd70`](https://github.com/ljharb/unbox-primitive/commit/810cd70f7b3c670cd55eae64466c89595175ee2a)
- [Tests] run `nyc` on all tests; use `tape` runner [`2f5fb08`](https://github.com/ljharb/unbox-primitive/commit/2f5fb08930c8f8e5e069ac61891dc9bd76cb762b)
- [meta] add `auto-changelog` [`03ed375`](https://github.com/ljharb/unbox-primitive/commit/03ed3759284493f19323eb0500f726d0851fc085)
- [actions] add automatic rebasing / merge commit blocking [`6dec48d`](https://github.com/ljharb/unbox-primitive/commit/6dec48daa357fa79a5cac1add9ca33f7b56276cc)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `object-is`, `tape` [`528ed88`](https://github.com/ljharb/unbox-primitive/commit/528ed8826664b67f7eaf1fe7e2031c063b2d315f)
- [actions] check out the entire repo [`5095b29`](https://github.com/ljharb/unbox-primitive/commit/5095b2981f44a78b3f9bfaa1a526f17a6823e383)
- [actions] add "Allow Edits" workflow [`5aa26d7`](https://github.com/ljharb/unbox-primitive/commit/5aa26d7f0c32e0e78ba4bf3e5f9abb5478fd97fa)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `object-is`, `safe-publish-latest`, `tape` [`afc18c6`](https://github.com/ljharb/unbox-primitive/commit/afc18c6cb59cbb6b514e0d8004c6fd264e2a27eb)
- [readme] remove travis badge [`a025899`](https://github.com/ljharb/unbox-primitive/commit/a0258997a21604e1266840e6d167f0a870966e9b)
- [Dev Deps] update `auto-changelog` [`9219a32`](https://github.com/ljharb/unbox-primitive/commit/9219a32844b2ce3ed0a7ea12a5910a3e92424e4e)
- [readme] Fix missing paren in example [`73f5a33`](https://github.com/ljharb/unbox-primitive/commit/73f5a3340ca1ab6c227ed4632117d816d5e35317)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`e450acc`](https://github.com/ljharb/unbox-primitive/commit/e450accb54ab452f240768a5f0a98e5887b0ba8c)
- [Deps] update `has-bigints`, `has-symbols`, `which-boxed-primitive` [`a4279b5`](https://github.com/ljharb/unbox-primitive/commit/a4279b504732002074e5dcb9c5509038d605f563)
- [Dev Deps] update `auto-changelog`, `in-publish`, `tape` [`b351548`](https://github.com/ljharb/unbox-primitive/commit/b351548d31789c0d0af4c3bce55c2bdefe51b40f)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`f600382`](https://github.com/ljharb/unbox-primitive/commit/f600382db83025270969354ac52a72aadb0a7ffa)
- [readme] fix travis links [`4d02fa9`](https://github.com/ljharb/unbox-primitive/commit/4d02fa9a4990812b048f8aefe6e46be80b68beef)
- [Dev Deps] update `auto-changelog`; add `aud` [`07e74a3`](https://github.com/ljharb/unbox-primitive/commit/07e74a3ca90688122593095849757e3c05c46db0)
- [meta] add `funding` field [`7ca4bd7`](https://github.com/ljharb/unbox-primitive/commit/7ca4bd71196e90a2fc9c7cb0ef4e30f949d5a853)
- [Tests] only audit prod deps [`47d8d5f`](https://github.com/ljharb/unbox-primitive/commit/47d8d5fbd58bf472e7e83f79ccef7e8379d06b35)
- [Deps] update `has-symbols` [`c70c15e`](https://github.com/ljharb/unbox-primitive/commit/c70c15e924191d11a271cff25bde657b0c3c3016)

## v1.0.0 - 2019-08-10

### Commits

- [Tests] add `.travis.yml` [`8c9a5ef`](https://github.com/ljharb/unbox-primitive/commit/8c9a5efdb54be4866e2884bf32cbe830788b2c2a)
- Initial commit [`feaff15`](https://github.com/ljharb/unbox-primitive/commit/feaff159eb999adc8763ff3e51d2d3d56d6164f8)
- [Tests] add tests [`3dd18d6`](https://github.com/ljharb/unbox-primitive/commit/3dd18d65748efb4af9b8ca66f8d8c5521d8f2dec)
- implementation [`472fb41`](https://github.com/ljharb/unbox-primitive/commit/472fb41d049ddee80ebf3219a5837e639a6e9341)
- npm init [`e9e426f`](https://github.com/ljharb/unbox-primitive/commit/e9e426fc90b9a3f07ffc48db75f78c414f77bc2b)
- [Tests] add linting [`139e74b`](https://github.com/ljharb/unbox-primitive/commit/139e74b94cdfd187b43b24de76c6d84af21ee467)
- [meta] create FUNDING.yml [`a9509e1`](https://github.com/ljharb/unbox-primitive/commit/a9509e122163e2b9d98af421e5c0575df36e2310)
- Only apps should have lockfiles [`b3d0834`](https://github.com/ljharb/unbox-primitive/commit/b3d0834d69dcbf4cbc1e61ccfaef05acf96cf630)
