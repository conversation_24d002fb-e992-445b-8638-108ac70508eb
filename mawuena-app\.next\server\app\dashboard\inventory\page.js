(()=>{var e={};e.id=454,e.ids=[454],e.modules={2295:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>i});var a=t(80261),s=t(44999);async function i(){let e=await (0,s.UL)();return(0,a.createServerClient)(process.env.NEXT_PUBLIC_SUPABASE_URL,process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:a})=>e.set(r,t,a))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(75986),s=t(8974);function i(...e){return(0,s.QP)((0,a.$)(e))}},11997:e=>{"use strict";e.exports=require("punycode")},13178:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23469:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(37413);t(61120);var s=t(70403),i=t(50662),n=t(10974);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:i=!1,...d}){let l=i?s.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:t,className:e})),...d})}},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),d=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:i="",children:n,iconNode:c,...u},h)=>(0,a.createElement)("svg",{ref:h,...l,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:o("lucide",i),...!n&&!d(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(n)?n:[n]])),u=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...i},d)=>(0,a.createElement)(c,{ref:d,iconNode:r,className:o(`lucide-${s(n(e))}`,`lucide-${e}`,t),...i}));return t.displayName=n(e),t}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41421:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>l});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["dashboard",{children:["inventory",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,49015)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Fluxitude\\Projects\\human-ai-collaboration-framework\\mawuena-app\\src\\app\\dashboard\\inventory\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/inventory/page",pathname:"/dashboard/inventory",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},42359:(e,r,t)=>{"use strict";t.d(r,{iD:()=>c,S6:()=>h,$5:()=>u});var a=t(67218);t(79130);var s=t(62351),i=t(39916),n=t(2507);async function o(){return await (0,n.U)()}async function d(e){let r=await o(),{data:t,error:a}=await r.from("users").insert(e).select().single();if(a)throw a;return t}async function l(e){let r=await o(),{data:t,error:a}=await r.from("users").select("*").eq("id",e).single();if(a)throw a;return t}async function c(e){let r=await (0,n.U)(),t=e.get("email"),a=e.get("password");t&&a||(0,i.redirect)("/login?error=missing-credentials");let{data:o,error:c}=await r.auth.signInWithPassword({email:t,password:a});if(c&&(0,i.redirect)(`/login?error=${encodeURIComponent(c.message)}`),o.user)try{await l(o.user.id)}catch{await d({id:o.user.id,email:o.user.email,instagram_handle:null,business_name:null,mastra_agent_config:{}})}(0,s.revalidatePath)("/","layout"),(0,i.redirect)("/dashboard")}async function u(e){let r=await (0,n.U)(),t=e.get("email"),a=e.get("password"),o=e.get("business_name");t&&a||(0,i.redirect)("/login?error=missing-credentials");let{data:l,error:c}=await r.auth.signUp({email:t,password:a,options:{emailRedirectTo:`${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`}});if(c&&(0,i.redirect)(`/login?error=${encodeURIComponent(c.message)}`),l.user)try{await d({id:l.user.id,email:l.user.email,instagram_handle:null,business_name:o||null,mastra_agent_config:{}})}catch(e){console.error("Error creating user profile:",e)}(0,s.revalidatePath)("/","layout"),(0,i.redirect)("/login?message=check-email")}async function h(){let e=await (0,n.U)(),{error:r}=await e.auth.signOut();r&&(0,i.redirect)("/error"),(0,s.revalidatePath)("/","layout"),(0,i.redirect)("/login")}(0,t(17478).D)([c,u,h]),(0,a.A)(c,"404ffb6408f3758ef54519a18c70686620246b4b96",null),(0,a.A)(u,"403b9a20870bf87801d4fe1364ea25b88da2e64fba",null),(0,a.A)(h,"00f2852461826a1a20213f241ac21fd695c07bc6e1",null)},47990:()=>{},49015:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(37413),s=t(39916),i=t(2507),n=t(23469),o=t(78963),d=t(26373);let l=(0,d.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),c=(0,d.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),u=(0,d.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]),h=(0,d.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var p=t(42359),m=t(4536),x=t.n(m);function v({children:e,title:r,description:t,user:s}){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:r}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:t})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("nav",{className:"flex gap-4",children:[(0,a.jsx)(n.$,{variant:"ghost",asChild:!0,children:(0,a.jsx)(x(),{href:"/dashboard",children:"Dashboard"})}),(0,a.jsx)(n.$,{variant:"ghost",asChild:!0,children:(0,a.jsx)(x(),{href:"/dashboard/inventory",children:"Inventory"})})]}),(0,a.jsx)("form",{action:p.S6,children:(0,a.jsx)(n.$,{variant:"outline",type:"submit",children:"Sign Out"})})]})]})}),(0,a.jsx)("div",{className:"px-4 py-6 sm:px-0",children:e})]})})}async function f(){let e=await (0,i.U)(),{data:r,error:t}=await e.auth.getUser();return(t||!r?.user)&&(0,s.redirect)("/login"),(0,a.jsxs)(v,{title:"Inventory",description:"Manage your clothing items and upload new inventory",user:r.user,children:[(0,a.jsxs)("div",{className:"flex justify-end gap-3 mb-6",children:[(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(l,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsxs)(x(),{href:"/dashboard/inventory/bulk-upload",children:[(0,a.jsx)(c,{className:"h-4 w-4 mr-2"}),"Add Items"]})})]}),(0,a.jsxs)(o.Zp,{className:"text-center py-12",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsx)(u,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsx)(o.ZB,{children:"No items in inventory"}),(0,a.jsx)(o.BT,{children:"Get started by uploading your first clothing items"})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsxs)(x(),{href:"/dashboard/inventory/bulk-upload",children:[(0,a.jsx)(h,{className:"h-4 w-4 mr-2"}),"Upload Items"]})}),(0,a.jsxs)(n.$,{variant:"outline",children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-2"}),"Import from CSV"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"You can upload multiple images at once and fill in details using our spreadsheet interface"})]})]})]})}},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},53346:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65343:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},67193:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78963:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var a=t(37413);t(61120);var s=t(10974);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80217:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},81630:e=>{"use strict";e.exports=require("http")},84924:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00f2852461826a1a20213f241ac21fd695c07bc6e1":()=>a.S6,"403b9a20870bf87801d4fe1364ea25b88da2e64fba":()=>a.$5,"404ffb6408f3758ef54519a18c70686620246b4b96":()=>a.iD});var a=t(42359)},91645:e=>{"use strict";e.exports=require("net")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var a=t(37413),s=t(22376),i=t.n(s),n=t(68726),o=t.n(n);t(61135);let d={title:"Create Next App",description:"Generated by create next app"};function l({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:e})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,145,875,658,923,805,877],()=>t(41421));module.exports=a})();