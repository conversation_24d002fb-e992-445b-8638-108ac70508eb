"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[848],{968:(e,r,t)=>{t.d(r,{b:()=>i});var o=t(2115),n=t(3655),a=t(5155),l=o.forwardRef((e,r)=>(0,a.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var i=l},2596:(e,r,t)=>{t.d(r,{$:()=>o});function o(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}},3655:(e,r,t)=>{t.d(r,{sG:()=>l});var o=t(2115);t(7650);var n=t(9708),a=t(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),l=o.forwardRef((e,o)=>{let{asChild:n,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?t:r,{...l,ref:o})});return l.displayName=`Primitive.${r}`,{...e,[r]:l}},{})},6101:(e,r,t)=>{t.d(r,{s:()=>l,t:()=>a});var o=t(2115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function l(...e){return o.useCallback(a(...e),e)}},6809:(e,r,t)=>{t.d(r,{UC:()=>es,B8:()=>el,bL:()=>ea,l9:()=>ei});var o,n=t(2115),a=t.t(n,2);function l(e,r,{checkForDefaultPrevented:t=!0}={}){return function(o){if(e?.(o),!1===t||!o.defaultPrevented)return r?.(o)}}var i=t(5155);function s(e,r=[]){let t=[],o=()=>{let r=t.map(e=>n.createContext(e));return function(t){let o=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return o.scopeName=e,[function(r,o){let a=n.createContext(o),l=t.length;t=[...t,o];let s=r=>{let{scope:t,children:o,...s}=r,c=t?.[e]?.[l]||a,d=n.useMemo(()=>s,Object.values(s));return(0,i.jsx)(c.Provider,{value:d,children:o})};return s.displayName=r+"Provider",[s,function(t,i){let s=i?.[e]?.[l]||a,c=n.useContext(s);if(c)return c;if(void 0!==o)return o;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((r,{useScope:t,scopeName:o})=>{let n=t(e)[`__scope${o}`];return{...r,...n}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:o}),[o])}};return t.scopeName=r.scopeName,t}(o,...r)]}function c(e,r,t){if(!r.has(e))throw TypeError("attempted to "+t+" private field on non-instance");return r.get(e)}function d(e,r){var t=c(e,r,"get");return t.get?t.get.call(e):t.value}function u(e,r,t){var o=c(e,r,"set");if(o.set)o.set.call(e,t);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=t}return t}var m=t(6101),f=t(9708),p=new WeakMap;function b(e,r){if("at"in Array.prototype)return Array.prototype.at.call(e,r);let t=function(e,r){let t=e.length,o=g(r),n=o>=0?o:t+o;return n<0||n>=t?-1:n}(e,r);return -1===t?void 0:e[t]}function g(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var h=globalThis?.document?n.useLayoutEffect:()=>{},v=a[" useId ".trim().toString()]||(()=>void 0),w=0;function y(e){let[r,t]=n.useState(v());return h(()=>{e||t(e=>e??String(w++))},[e]),e||(r?`radix-${r}`:"")}var x=t(3655),k=a[" useInsertionEffect ".trim().toString()]||h;function N({prop:e,defaultProp:r,onChange:t=()=>{},caller:o}){let[a,l,i]=function({defaultProp:e,onChange:r}){let[t,o]=n.useState(e),a=n.useRef(t),l=n.useRef(r);return k(()=>{l.current=r},[r]),n.useEffect(()=>{a.current!==t&&(l.current?.(t),a.current=t)},[t,a]),[t,o,l]}({defaultProp:r,onChange:t}),s=void 0!==e,c=s?e:a;{let r=n.useRef(void 0!==e);n.useEffect(()=>{let e=r.current;if(e!==s){let r=s?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${r}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}r.current=s},[s,o])}return[c,n.useCallback(r=>{if(s){let t="function"==typeof r?r(e):r;t!==e&&i.current?.(t)}else l(r)},[s,e,l,i])]}Symbol("RADIX:SYNC_STATE");var z=n.createContext(void 0);function M(e){let r=n.useContext(z);return e||r||"ltr"}var R="rovingFocusGroup.onEntryFocus",C={bubbles:!1,cancelable:!0},j="RovingFocusGroup",[E,I,T]=function(e){let r=e+"CollectionProvider",[t,o]=s(r),[a,l]=t(r,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:r,children:t}=e,o=n.useRef(null),l=n.useRef(new Map).current;return(0,i.jsx)(a,{scope:r,itemMap:l,collectionRef:o,children:t})};c.displayName=r;let d=e+"CollectionSlot",u=(0,f.TL)(d),p=n.forwardRef((e,r)=>{let{scope:t,children:o}=e,n=l(d,t),a=(0,m.s)(r,n.collectionRef);return(0,i.jsx)(u,{ref:a,children:o})});p.displayName=d;let b=e+"CollectionItemSlot",g="data-radix-collection-item",h=(0,f.TL)(b),v=n.forwardRef((e,r)=>{let{scope:t,children:o,...a}=e,s=n.useRef(null),c=(0,m.s)(r,s),d=l(b,t);return n.useEffect(()=>(d.itemMap.set(s,{ref:s,...a}),()=>void d.itemMap.delete(s))),(0,i.jsx)(h,{...{[g]:""},ref:c,children:o})});return v.displayName=b,[{Provider:c,Slot:p,ItemSlot:v},function(r){let t=l(e+"CollectionConsumer",r);return n.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(t.itemMap.values()).sort((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},o]}(j),[S,A]=s(j,[T]),[P,D]=S(j),O=n.forwardRef((e,r)=>(0,i.jsx)(E.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(E.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,i.jsx)(_,{...e,ref:r})})}));O.displayName=j;var _=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:o,loop:a=!1,dir:s,currentTabStopId:c,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:u,onEntryFocus:f,preventScrollOnEntryFocus:p=!1,...b}=e,g=n.useRef(null),h=(0,m.s)(r,g),v=M(s),[w,y]=N({prop:c,defaultProp:null!=d?d:null,onChange:u,caller:j}),[k,z]=n.useState(!1),E=function(e){let r=n.useRef(e);return n.useEffect(()=>{r.current=e}),n.useMemo(()=>(...e)=>r.current?.(...e),[])}(f),T=I(t),S=n.useRef(!1),[A,D]=n.useState(0);return n.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(R,E),()=>e.removeEventListener(R,E)},[E]),(0,i.jsx)(P,{scope:t,orientation:o,dir:v,loop:a,currentTabStopId:w,onItemFocus:n.useCallback(e=>y(e),[y]),onItemShiftTab:n.useCallback(()=>z(!0),[]),onFocusableItemAdd:n.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>D(e=>e-1),[]),children:(0,i.jsx)(x.sG.div,{tabIndex:k||0===A?-1:0,"data-orientation":o,...b,ref:h,style:{outline:"none",...e.style},onMouseDown:l(e.onMouseDown,()=>{S.current=!0}),onFocus:l(e.onFocus,e=>{let r=!S.current;if(e.target===e.currentTarget&&r&&!k){let r=new CustomEvent(R,C);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);L([e.find(e=>e.active),e.find(e=>e.id===w),...e].filter(Boolean).map(e=>e.ref.current),p)}}S.current=!1}),onBlur:l(e.onBlur,()=>z(!1))})})}),F="RovingFocusGroupItem",G=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:o=!0,active:a=!1,tabStopId:s,children:c,...d}=e,u=y(),m=s||u,f=D(F,t),p=f.currentTabStopId===m,b=I(t),{onFocusableItemAdd:g,onFocusableItemRemove:h,currentTabStopId:v}=f;return n.useEffect(()=>{if(o)return g(),()=>h()},[o,g,h]),(0,i.jsx)(E.ItemSlot,{scope:t,id:m,focusable:o,active:a,children:(0,i.jsx)(x.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...d,ref:r,onMouseDown:l(e.onMouseDown,e=>{o?f.onItemFocus(m):e.preventDefault()}),onFocus:l(e.onFocus,()=>f.onItemFocus(m)),onKeyDown:l(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var o;let n=(o=e.key,"rtl"!==t?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(n)))return $[n]}(e,f.orientation,f.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let o=t.indexOf(e.currentTarget);t=f.loop?function(e,r){return e.map((t,o)=>e[(r+o)%e.length])}(t,o+1):t.slice(o+1)}setTimeout(()=>L(t))}}),children:"function"==typeof c?c({isCurrentTabStop:p,hasTabStop:null!=v}):c})})});G.displayName=F;var $={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let o of e)if(o===t||(o.focus({preventScroll:r}),document.activeElement!==t))return}var U=e=>{let{present:r,children:t}=e,o=function(e){var r,t;let[o,a]=n.useState(),l=n.useRef(null),i=n.useRef(e),s=n.useRef("none"),[c,d]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,r)=>{let o=t[e][r];return null!=o?o:e},r));return n.useEffect(()=>{let e=W(l.current);s.current="mounted"===c?e:"none"},[c]),h(()=>{let r=l.current,t=i.current;if(t!==e){let o=s.current,n=W(r);e?d("MOUNT"):"none"===n||(null==r?void 0:r.display)==="none"?d("UNMOUNT"):t&&o!==n?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),h(()=>{if(o){var e;let r,t=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=W(l.current).includes(e.animationName);if(e.target===o&&n&&(d("ANIMATION_END"),!i.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",r=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=W(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(r),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(r),a="function"==typeof t?t({present:o.isPresent}):n.Children.only(t),l=(0,m.s)(o.ref,function(e){var r,t;let o=null==(r=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:r.get,n=o&&"isReactWarning"in o&&o.isReactWarning;return n?e.ref:(n=(o=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof t||o.isPresent?n.cloneElement(a,{ref:l}):null};function W(e){return(null==e?void 0:e.animationName)||"none"}U.displayName="Presence";var K="Tabs",[V,B]=s(K,[A]),q=A(),[H,Q]=V(K),X=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,onValueChange:n,defaultValue:a,orientation:l="horizontal",dir:s,activationMode:c="automatic",...d}=e,u=M(s),[m,f]=N({prop:o,onChange:n,defaultProp:null!=a?a:"",caller:K});return(0,i.jsx)(H,{scope:t,baseId:y(),value:m,onValueChange:f,orientation:l,dir:u,activationMode:c,children:(0,i.jsx)(x.sG.div,{dir:u,"data-orientation":l,...d,ref:r})})});X.displayName=K;var Y="TabsList",Z=n.forwardRef((e,r)=>{let{__scopeTabs:t,loop:o=!0,...n}=e,a=Q(Y,t),l=q(t);return(0,i.jsx)(O,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:o,children:(0,i.jsx)(x.sG.div,{role:"tablist","aria-orientation":a.orientation,...n,ref:r})})});Z.displayName=Y;var J="TabsTrigger",ee=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,disabled:n=!1,...a}=e,s=Q(J,t),c=q(t),d=eo(s.baseId,o),u=en(s.baseId,o),m=o===s.value;return(0,i.jsx)(G,{asChild:!0,...c,focusable:!n,active:m,children:(0,i.jsx)(x.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:d,...a,ref:r,onMouseDown:l(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(o)}),onKeyDown:l(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(o)}),onFocus:l(e.onFocus,()=>{let e="manual"!==s.activationMode;m||n||!e||s.onValueChange(o)})})})});ee.displayName=J;var er="TabsContent",et=n.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,forceMount:a,children:l,...s}=e,c=Q(er,t),d=eo(c.baseId,o),u=en(c.baseId,o),m=o===c.value,f=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.jsx)(U,{present:a||m,children:t=>{let{present:o}=t;return(0,i.jsx)(x.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!o,id:u,tabIndex:0,...s,ref:r,style:{...e.style,animationDuration:f.current?"0s":void 0},children:o&&l})}})});function eo(e,r){return"".concat(e,"-trigger-").concat(r)}function en(e,r){return"".concat(e,"-content-").concat(r)}et.displayName=er;var ea=X,el=Z,ei=ee,es=et},9688:(e,r,t)=>{t.d(r,{QP:()=>ec});let o=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),n(t,r)||l(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,l=e=>{if(a.test(e)){let r=a.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},i=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)s(t[e],o,e,r);return o},s=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:c(r,e)).classGroupId=t;return}if("function"==typeof e)return d(e)?void s(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{s(n,c(r,e),t,o)})})},c=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},m=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,n=0,a=0;for(let l=0;l<e.length;l++){let i=e[l];if(0===o&&0===n){if(":"===i){t.push(e.slice(a,l)),a=l+1;continue}if("/"===i){r=l;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let l=0===t.length?e:e.substring(a),i=f(l);return{modifiers:t,hasImportantModifier:i!==l,baseClassName:i,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},f=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},b=e=>({cache:u(e.cacheSize),parseClassName:m(e),sortModifiers:p(e),...o(e)}),g=/\s+/,h=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],i=e.trim().split(g),s="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:f}=t(r);if(c){s=r+(s.length>0?" "+s:s);continue}let p=!!f,b=o(p?m.substring(0,f):m);if(!b){if(!p||!(b=o(m))){s=r+(s.length>0?" "+s:s);continue}p=!1}let g=a(d).join(":"),h=u?g+"!":g,v=h+b;if(l.includes(v))continue;l.push(v);let w=n(b,p);for(let e=0;e<w.length;++e){let r=w[e];l.push(h+r)}s=r+(s.length>0?" "+s:s)}return s};function v(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=w(e))&&(o&&(o+=" "),o+=r);return o}let w=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=w(e[o]))&&(t&&(t+=" "),t+=r);return t},y=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,N=/^\d+\/\d+$/,z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,M=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,R=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,C=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,j=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=e=>N.test(e),I=e=>!!e&&!Number.isNaN(Number(e)),T=e=>!!e&&Number.isInteger(Number(e)),S=e=>e.endsWith("%")&&I(e.slice(0,-1)),A=e=>z.test(e),P=()=>!0,D=e=>M.test(e)&&!R.test(e),O=()=>!1,_=e=>C.test(e),F=e=>j.test(e),G=e=>!L(e)&&!q(e),$=e=>ee(e,en,O),L=e=>x.test(e),U=e=>ee(e,ea,D),W=e=>ee(e,el,I),K=e=>ee(e,et,O),V=e=>ee(e,eo,F),B=e=>ee(e,es,_),q=e=>k.test(e),H=e=>er(e,ea),Q=e=>er(e,ei),X=e=>er(e,et),Y=e=>er(e,en),Z=e=>er(e,eo),J=e=>er(e,es,!0),ee=(e,r,t)=>{let o=x.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=k.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,el=e=>"number"===e,ei=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...r){let t,o,n,a=function(i){return o=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,a=l,l(i)};function l(e){let r=o(e);if(r)return r;let a=h(e,t);return n(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=y("color"),r=y("font"),t=y("text"),o=y("font-weight"),n=y("tracking"),a=y("leading"),l=y("breakpoint"),i=y("container"),s=y("spacing"),c=y("radius"),d=y("shadow"),u=y("inset-shadow"),m=y("text-shadow"),f=y("drop-shadow"),p=y("blur"),b=y("perspective"),g=y("aspect"),h=y("ease"),v=y("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),q,L],N=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto","contain","none"],M=()=>[q,L,s],R=()=>[E,"full","auto",...M()],C=()=>[T,"none","subgrid",q,L],j=()=>["auto",{span:["full",T,q,L]},T,q,L],D=()=>[T,"auto",q,L],O=()=>["auto","min","max","fr",q,L],_=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...M()],er=()=>[E,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...M()],et=()=>[e,q,L],eo=()=>[...x(),X,K,{position:[q,L]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",Y,$,{size:[q,L]}],el=()=>[S,H,U],ei=()=>["","none","full",c,q,L],es=()=>["",I,H,U],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[I,S,X,K],em=()=>["","none",p,q,L],ef=()=>["none",I,q,L],ep=()=>["none",I,q,L],eb=()=>[I,q,L],eg=()=>[E,"full",...M()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[P],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[G],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",I],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",E,L,q,g]}],container:["container"],columns:[{columns:[I,L,q,i]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:R()}],"inset-x":[{"inset-x":R()}],"inset-y":[{"inset-y":R()}],start:[{start:R()}],end:[{end:R()}],top:[{top:R()}],right:[{right:R()}],bottom:[{bottom:R()}],left:[{left:R()}],visibility:["visible","invisible","collapse"],z:[{z:[T,"auto",q,L]}],basis:[{basis:[E,"full","auto",i,...M()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[I,E,"auto","initial","none",L]}],grow:[{grow:["",I,q,L]}],shrink:[{shrink:["",I,q,L]}],order:[{order:[T,"first","last","none",q,L]}],"grid-cols":[{"grid-cols":C()}],"col-start-end":[{col:j()}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":C()}],"row-start-end":[{row:j()}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":O()}],"auto-rows":[{"auto-rows":O()}],gap:[{gap:M()}],"gap-x":[{"gap-x":M()}],"gap-y":[{"gap-y":M()}],"justify-content":[{justify:[..._(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",..._()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":_()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:M()}],px:[{px:M()}],py:[{py:M()}],ps:[{ps:M()}],pe:[{pe:M()}],pt:[{pt:M()}],pr:[{pr:M()}],pb:[{pb:M()}],pl:[{pl:M()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":M()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":M()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[i,"screen",...er()]}],"min-w":[{"min-w":[i,"screen","none",...er()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,H,U]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,q,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",S,L]}],"font-family":[{font:[Q,L,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,q,L]}],"line-clamp":[{"line-clamp":[I,"none",q,W]}],leading:[{leading:[a,...M()]}],"list-image":[{"list-image":["none",q,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",q,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[I,"from-font","auto",q,U]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[I,"auto",q,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},T,q,L],radial:["",q,L],conic:[T,q,L]},Z,V]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[I,q,L]}],"outline-w":[{outline:["",I,H,U]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",d,J,B]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",u,J,B]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[I,U]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",m,J,B]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[I,q,L]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[I]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[q,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[I]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",q,L]}],filter:[{filter:["","none",q,L]}],blur:[{blur:em()}],brightness:[{brightness:[I,q,L]}],contrast:[{contrast:[I,q,L]}],"drop-shadow":[{"drop-shadow":["","none",f,J,B]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",I,q,L]}],"hue-rotate":[{"hue-rotate":[I,q,L]}],invert:[{invert:["",I,q,L]}],saturate:[{saturate:[I,q,L]}],sepia:[{sepia:["",I,q,L]}],"backdrop-filter":[{"backdrop-filter":["","none",q,L]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[I,q,L]}],"backdrop-contrast":[{"backdrop-contrast":[I,q,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",I,q,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[I,q,L]}],"backdrop-invert":[{"backdrop-invert":["",I,q,L]}],"backdrop-opacity":[{"backdrop-opacity":[I,q,L]}],"backdrop-saturate":[{"backdrop-saturate":[I,q,L]}],"backdrop-sepia":[{"backdrop-sepia":["",I,q,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":M()}],"border-spacing-x":[{"border-spacing-x":M()}],"border-spacing-y":[{"border-spacing-y":M()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",q,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[I,"initial",q,L]}],ease:[{ease:["linear","initial",h,q,L]}],delay:[{delay:[I,q,L]}],animate:[{animate:["none",v,q,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,q,L]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[q,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q,L]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[I,H,U,W]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,r,t)=>{t.d(r,{TL:()=>l});var o=t(2115),n=t(6101),a=t(5155);function l(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...a}=e;if(o.isValidElement(t)){var l;let e,i,s=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,r){let t={...r};for(let o in r){let n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...a}:"className"===o&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==o.Fragment&&(c.ref=r?(0,n.t)(r,s):s),o.cloneElement(t,c)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...l}=e,i=o.Children.toArray(n),c=i.find(s);if(c){let e=c.props.children,n=i.map(r=>r!==c?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...l,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var i=Symbol("radix.slottable");function s(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}}]);